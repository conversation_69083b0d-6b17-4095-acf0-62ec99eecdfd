# API Testing Guide for Enhanced Subscriber Implementation

## Implementation Summary

The `getAllUser(Integer hasCourse)` method has been **enhanced** (not replaced) with:

- **New signature**: `getAllUser(Integer hasCourse, String userType, Pageable pageable)`
- **Backward compatibility**: Original method still works via method overloading
- **Repository-level sorting**: Moved sorting logic to repository layer with `@Query` annotations
- **Integrated subscriber data**: Subscribers are merged with users in the same response
- **Pagination support**: Returns `Page<MarketingUserDTO>` for paginated requests

## 1. Test Subscriber Creation Endpoint

### Valid Subscriber Creation

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "<EMAIL>",
  "isSubscriber": true
}
```

### Invalid Email Test

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "invalid-email",
  "isSubscriber": true
}
```

### Duplicate Email Test

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "<EMAIL>",
  "isSubscriber": true
}
```

## 2. Test Enhanced getAllUser Endpoint

### Test Backward Compatibility

```bash
# Original API call - should still work exactly as before
GET /users

# With hasCourse parameter - should still work
GET /users?hasCourse=1
```

### Test New Pagination Features

```bash
GET /users?page=0&size=5
```

### Get Only Subscribers

```bash
GET /users?userType=SUBSCRIBER&page=0&size=10
```

### Get Only Regular Users

```bash
GET /users?userType=USER&page=0&size=10
```

### Get All Types

```bash
GET /users?userType=ALL&page=0&size=10
```

## 3. Database Verification

After running the application, verify the subscriber table was created automatically by Hibernate:

```sql
DESCRIBE subscriber;
SELECT * FROM subscriber;
```

**Note**: The table is created automatically by Hibernate from the JPA @Entity model, not from schema files.

## 4. Expected Response Format

### Subscriber Creation Response

```json
{
  "status": "OK",
  "success": true,
  "data": {
    "subscriberId": 1,
    "email": "<EMAIL>",
    "isSubscriber": true,
    "createdAt": "2024-01-15T10:30:00.000+00:00",
    "updatedAt": "2024-01-15T10:30:00.000+00:00"
  },
  "message": "User created successfully"
}
```

### Enhanced getAllUser Response (Paginated)

```json
{
  "status": "OK",
  "success": true,
  "data": {
    "content": [
      {
        "userName": "John Doe",
        "mobileNo": "1234567890",
        "emailId": "<EMAIL>",
        "createdOn": "2024-01-15T10:30:00.000+00:00",
        "isActive": 1,
        "userType": "USER"
      },
      {
        "userName": "Subscriber",
        "mobileNo": null,
        "emailId": "<EMAIL>",
        "createdOn": "2024-01-15T10:35:00.000+00:00",
        "isActive": 1,
        "userType": "SUBSCRIBER"
      }
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10
    },
    "totalElements": 2,
    "totalPages": 1,
    "first": true,
    "last": true
  },
  "message": "User details retrieved successfully"
}
```
