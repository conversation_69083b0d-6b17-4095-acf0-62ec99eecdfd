# API Testing Guide for Clean Subscriber Implementation

## ✅ Final Clean Implementation Summary

The `getAllUser` method has been **completely refactored** with a clean, simple architecture:

### **🎯 New Method Signature**

- **Enhanced**: `getAllUser(Integer hasCourse, String userType, Pageable pageable)`
- **Deprecated**: `getAllUser(Integer hasCourse)` - marked for removal

### **🏗️ Clean Architecture**

- **Single Responsibility**: Each method handles one specific scenario
- **Repository-Level Pagination**: All pagination handled in repository layer
- **No Mixed Logic**: Clear separation between single and combined data sources
- **Simplified Conditionals**: Clean if-else structure with dedicated methods

### **🔄 Filtering Logic**

- `userType=null` → Returns ALL data (users + subscribers merged)
- `userType="USER"` → Returns ONLY regular users (repository pagination)
- `userType="SUBSCRIBER"` → Returns ONLY subscribers (repository pagination)

### **📊 Pagination Strategy**

- **Single Data Source**: Pure repository pagination (efficient)
- **Combined Data**: Manual pagination after merging (only when userType=null)
- **Controller Layer**: Only handles HTTP concerns and parameter mapping

## 1. Test Subscriber Creation Endpoint

### Valid Subscriber Creation

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "<EMAIL>",
  "isSubscriber": true
}
```

### Invalid Email Test

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "invalid-email",
  "isSubscriber": true
}
```

### Duplicate Email Test

```bash
POST /api/users/subscribers
Content-Type: application/json

{
  "email": "<EMAIL>",
  "isSubscriber": true
}
```

## 2. Test Enhanced getAllUser Endpoint

### Test Backward Compatibility

```bash
# Original API call - should still work exactly as before
GET /users

# With hasCourse parameter - should still work
GET /users?hasCourse=1
```

### Test New Pagination Features

```bash
GET /users?page=0&size=5
```

### Get All Data (Users + Subscribers)

```bash
# When userType is omitted, returns both users and subscribers
GET /users?page=0&size=10
```

### Get Only Regular Users

```bash
GET /users?userType=USER&page=0&size=10
```

### Get Only Subscribers

```bash
GET /users?userType=SUBSCRIBER&page=0&size=10
```

## 3. Database Verification

After running the application, verify the subscriber table was created automatically by Hibernate:

```sql
DESCRIBE subscriber;
SELECT * FROM subscriber;
```

**Note**: The table is created automatically by Hibernate from the JPA @Entity model, not from schema files.

## 4. Expected Response Format

### Subscriber Creation Response

```json
{
  "status": "OK",
  "success": true,
  "data": {
    "subscriberId": 1,
    "email": "<EMAIL>",
    "isSubscriber": true,
    "createdAt": "2024-01-15T10:30:00.000+00:00",
    "updatedAt": "2024-01-15T10:30:00.000+00:00"
  },
  "message": "User created successfully"
}
```

### Enhanced getAllUser Response (Paginated)

```json
{
  "status": "OK",
  "success": true,
  "data": {
    "content": [
      {
        "userName": "John Doe",
        "mobileNo": "1234567890",
        "emailId": "<EMAIL>",
        "createdOn": "2024-01-15T10:30:00.000+00:00",
        "isActive": 1,
        "userType": "USER"
      },
      {
        "userName": "Subscriber",
        "mobileNo": null,
        "emailId": "<EMAIL>",
        "createdOn": "2024-01-15T10:35:00.000+00:00",
        "isActive": 1,
        "userType": "SUBSCRIBER"
      }
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10
    },
    "totalElements": 2,
    "totalPages": 1,
    "first": true,
    "last": true
  },
  "message": "User details retrieved successfully"
}
```
