package com.job.jobportal.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;

@Component
public class DatabaseInitializer {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private Environment environment;

    @PostConstruct
    public void initializeDatabase() throws Exception {
        // Check if initialization is enabled
        boolean isInitializationEnabled = Boolean.parseBoolean(
                environment.getProperty("app.database.initialize", "false")
        );

        if (!isInitializationEnabled) {
            System.out.println("Database initialization skipped.");
            return;
        }

        // Execute schema2.sql first
        executeSchemaFile("schema2.sql");

        // Execute schema3.sql for subscriber table
        executeSchemaFile("schema3.sql");
    }

    private void executeSchemaFile(String fileName) throws Exception {
        try {
            Resource resource = resourceLoader.getResource("classpath:" + fileName);
            String sql = new String(Files.readAllBytes(Paths.get(resource.getURI())));

            // Split SQL script using custom delimiter $$ (for schema2.sql) or semicolon (for schema3.sql)
            String[] statements;
            if (fileName.equals("schema2.sql")) {
                statements = sql.split("\\$\\$");
            } else {
                statements = sql.split(";");
            }

            System.out.println("Executing " + fileName + "...");
            for (String statement : statements) {
                if (!statement.trim().isEmpty()) {
                    System.out.println("------------");
                    System.out.println(statement.trim());
                    System.out.println("------------");
                    jdbcTemplate.execute(statement.trim());
                }
            }
            System.out.println(fileName + " executed successfully.");
        } catch (Exception e) {
            System.err.println("Error executing " + fileName + ": " + e.getMessage());
            // Don't throw exception for schema3.sql to avoid breaking existing functionality
            if (fileName.equals("schema2.sql")) {
                throw e;
            }
        }
    }
}
