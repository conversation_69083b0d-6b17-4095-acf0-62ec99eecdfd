package com.job.jobportal.controller;


import com.job.jobportal.dto.MarketingUserDTO;
import com.job.jobportal.dto.NotificationDTO;
import com.job.jobportal.dto.SubscriberDTO;
import com.job.jobportal.dto.SurveyDTO;
import com.job.jobportal.dto.UserDTO;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.SubscriberService;
import com.job.jobportal.service.SurveyService;
import com.job.jobportal.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/")
public class UserController {

    @Autowired
    MessageSource message;

    @Autowired
    UserService userService;

    @Autowired
    SurveyService surveyService;

    @Autowired
    SubscriberService subscriberService;

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @GetMapping("/users")
    public ResponseEntity<?> getAllUser(
            @RequestParam(required = false) Integer hasCourse,
            @RequestParam(required = false) String userType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        // For backward compatibility, if no pagination parameters are provided, use the original method
        if (page == 0 && size == 10 && userType == null) {
            List<MarketingUserDTO> marketingUserDTOS = userService.getAllUser(hasCourse);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserDTOS, message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        }

        // Use paginated method
        Pageable pageable = PageRequest.of(page, size);
        Page<MarketingUserDTO> marketingUserPage = userService.getAllUserPaginated(hasCourse, userType, pageable);
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserPage, message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
    }


    @PostMapping("user")
    public ResponseEntity<?> addUser(@RequestBody @Valid UserDTO user) throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, userService.saveUser(user),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//	@GetMapping()
//	public ResponseEntity<?> currentUser(@RegisteredOAuth2AuthorizedClient("google") OAuth2AuthorizedClient user) throws Exception {
//		try {
//			userService.updateRefreshToken( user.getRefreshToken().getTokenValue(),user.getPrincipalName());
//			return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, user,
//					message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())),HttpStatus.OK);
//		}
//
//		catch (Exception e) {
//			return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
//		}
//	}

    @GetMapping("user")
    public ResponseEntity<?> getUser() throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, userService.getUser(),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @GetMapping("alluser")
    public ResponseEntity<?> getAllUser(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String email,
            @RequestParam(defaultValue = "firstName") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws Exception {
        try {
            if (role == null && email == null &&
                sortBy.equals("firstName") && sortDirection.equals("asc") &&
                page == 0 && size == 10) {
                return new ResponseEntity<>(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    userService.getAllUsersAsDTO(),
                    message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())),
                    HttpStatus.OK);
            }

            return new ResponseEntity<>(new ApiResponse<>(
                HttpStatus.OK,
                true,
                userService.getFilteredUsers(role, email, sortBy, sortDirection, page, size),
                message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())),
                HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("user/survey")
    public ResponseEntity<?> updateSurvey(@RequestBody SurveyDTO surveyDTO) {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, surveyService.updateSurvey(surveyDTO),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("notificationToken")
    public ResponseEntity<?> addUser(@RequestBody NotificationDTO notificationDTO) throws Exception {
        try {
            userService.updateNotificationToken(notificationDTO.getToken());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("api/users/subscribers")
    public ResponseEntity<?> addSubscriber(@RequestBody @Valid SubscriberDTO subscriberDTO) throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, subscriberService.saveSubscriber(subscriberDTO),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
