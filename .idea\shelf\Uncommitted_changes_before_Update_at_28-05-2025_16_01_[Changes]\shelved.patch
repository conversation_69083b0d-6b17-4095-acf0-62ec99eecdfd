Index: src/main/resources/application-dev.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#spring.jpa.hibernate.ddl-auto=update\r\n#spring.jpa.hibernate.ddl-auto=create-drop\r\nspring.jpa.hibernate.ddl-auto=create\r\n#spring.jpa.hibernate.ddl-auto=none\r\nspring.datasource.url=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# application.baseFrontendUrl=http://app.localhost:3000\r\napplication.baseFrontendUrl=http://localhost:3000\r\nmanagement.endpoints.web.exposure.include=health,info,metrics\r\n# Optionally, configure access to the health endpoint\r\nmanagement.endpoint.health.probe.enabled=true\r\nmanagement.endpoints.web.base-path=/actuator\r\nmanagement.endpoint.health.show-details=always\r\napplication.ticketEmail=<EMAIL>\r\napplication.certificatecode=JB\r\napplication.name=Job Portal\r\napplication.description=Portal for Applying Jobs under several department\r\napplication.version=1.0.0\r\napp.base-url=https://www.groglojobs.co.uk\r\nserver.port=8080\r\nspring.sql.init.mode=embedded\r\n#spring.sql.init.mode=never\r\napp.database.initialize=true\r\nspring.jpa.open-in-view=false\r\nspring.jpa.defer-datasource-initialization=true\r\n#spring.flyway.enabled=true\r\n#spring.jpa.defer-datasource-initialization=false\r\nspring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER\r\nspring.servlet.multipart.max-file-size=-1\r\nspring.servlet.multipart.max-request-size=-1\r\napplication.aws.bucketname=ebrainyvideostreaming\r\napplication.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com\r\napplication.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net\r\napplication.aws.accessKey=********************\r\napplication.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM\r\napplication.aws.region=eu-north-1\r\napplication.aws.secretName=stripe\r\n\r\napplication.email=<EMAIL>\r\n#email.domain.from=<EMAIL>\r\n\r\n# Social media links\r\nsocial.facebook.url=https://www.facebook.com/groglojobs\r\nsocial.linkedin.url=https://www.linkedin.com/company/groglojobs\r\nsocial.twitter.url=https://twitter.com/groglojobs\r\n\r\n# Social media icons\r\nsocial.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png\r\nsocial.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png\r\nsocial.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png\r\n\r\n\r\n#mail\r\nemail.provider=domain\r\n\r\n# gmail\r\nspring.mail.host=smtp.gmail.com\r\nspring.mail.port=587\r\nspring.mail.username=<EMAIL>\r\nspring.mail.password=aoamtqqqspnwjuvu\r\n#domain\r\n#email.domain.host=smtp.zoho.in\r\n#email.domain.port=465\r\n#email.domain.username=<EMAIL>\r\n#email.domain.password=rfxVXje1NXUz\r\n#email.domain.from=<EMAIL>\r\nemail.domain.host=smtp.zoho.eu\r\nemail.domain.port=465\r\nemail.domain.username=<EMAIL>\r\nemail.domain.password=vNsnxLakWUnr\r\nemail.domain.from=<EMAIL>\r\n\r\n\r\n\r\nspring.mail.properties.mail.smtp.auth=true\r\nspring.mail.properties.mail.smtp.starttls.enable=true\r\n\r\n#sms\r\nsms.PHONE_NUMBER=+16203901757\r\n#payment\r\n# Razorpay\r\nstripe.webhook.signing.currency=GBP\r\napplication.multiCurrency=false\r\napplication.multiCurrencyList=USD,GBP,EUR\r\n#security\r\nspring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com\r\nspring.security.oauth2.client.registration.google.clientSecret=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\r\nspring.security.oauth2.client.registration.google.scope=email, profile\r\nspring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.registration.facebook.clientId=1456102035268496\r\nspring.security.oauth2.client.registration.facebook.clientSecret=********************************\r\nspring.security.oauth2.client.registration.facebook.scope=email, public_profile\r\nspring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth\r\nspring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token\r\n#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified\r\napp.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1\r\napp.auth.tokenExpirationMsec=864000000\r\napp.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*\r\napp.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect\r\n#logs\r\nlogging.level.root=INFO\r\nlogging.config=classpath:logback-spring.xml\r\nlogging.level.org.springframework=INFO\r\nlogging.level.org.springframework.boot=INFO\r\nlogging.level.org.springframework.boot.autoconfigure=INFO\r\nlogging.level.org.springframework.boot.context=INFO\r\nlogging.level.org.springframework.boot.devtools=INFO\r\nlogging.level.org.springframework.web=INFO\r\nspring.devtools.restart.enabled=false\r\n\r\n# stripe\r\nstripe.api.key=sk_test_51R6oOD4YEQBprOqE5njXOiLJbv3yOi3XZUVKOgMUpeSFBxG1BB6h9DMUR3QRJYkCKisdksd1UmAxEp5Y5OVNKTG1002yeP6bV1\r\nstripe.webhook.signing.secret=whsec_3e18ca3d7014104d8b29a889e2ead2513989296abded91963fd6ae676d931ba6\r\nstripe.price.standard.monthly=price_1R78mJ4YEQBprOqE5tTXs86x\r\nstripe.price.standard.yearly=price_standard_yearly_id\r\nstripe.price.premium.monthly=price_1R78mw4YEQBprOqEDP69lLFW\r\nstripe.price.premium.yearly=price_premium_yearly_id\r\nstripe.price.enterprise.monthly=price_enterprise_monthly_id\r\nstripe.price.enterprise.yearly=price_enterprise_yearly_id\r\n\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/src/main/resources/application-dev.properties b/src/main/resources/application-dev.properties
--- a/src/main/resources/application-dev.properties	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/src/main/resources/application-dev.properties	(date 1748428156753)
@@ -4,7 +4,7 @@
 #spring.jpa.hibernate.ddl-auto=none
 spring.datasource.url=*************************************
 spring.datasource.username=root
-spring.datasource.password=system123#
+spring.datasource.password=root
 spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
 #spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
 spring.jpa.show-sql=true
Index: pom.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns=\"http://maven.apache.org/POM/4.0.0\"\r\n         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd\">\r\n    <modelVersion>4.0.0</modelVersion>\r\n\r\n    <parent>\r\n        <groupId>org.springframework.boot</groupId>\r\n        <artifactId>spring-boot-starter-parent</artifactId>\r\n        <version>3.3.0</version>\r\n        <relativePath/> <!-- lookup parent from repository -->\r\n    </parent>\r\n    <groupId>com.job</groupId>\r\n    <artifactId>jobportal</artifactId>\r\n    <version>0.0.1-SNAPSHOT</version>\r\n    <name>jobportal</name>\r\n    <description>This is backend application for job portal</description>\r\n    <properties>\r\n        <java.version>17</java.version>\r\n        <maven.test.skip>true</maven.test.skip>\r\n\r\n    </properties>\r\n    <dependencies>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-data-jpa</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.google.auth</groupId>\r\n            <artifactId>google-auth-library-oauth2-http</artifactId>\r\n            <version>1.24.1</version>\r\n        </dependency>\r\n        <!-- Google API Client -->\r\n        <dependency>\r\n            <groupId>com.google.api-client</groupId>\r\n            <artifactId>google-api-client</artifactId>\r\n            <version>1.34.1</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-actuator</artifactId>\r\n        </dependency>\r\n        <!-- pdf generation\r\n        <dependency>\r\n            <groupId>com.itextpdf</groupId>\r\n            <artifactId>kernel</artifactId>\r\n            <version>7.2.6</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.itextpdf</groupId>\r\n            <artifactId>layout</artifactId>\r\n            <version>7.2.6</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.itextpdf</groupId>\r\n            <artifactId>io</artifactId>\r\n            <version>7.2.6</version>\r\n        </dependency> -->\r\n\r\n     <!--   <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-hateoas</artifactId>\r\n        </dependency>-->\r\n\r\n        <!-- Jackson JSON Processor -->\r\n        <dependency>\r\n            <groupId>com.google.http-client</groupId>\r\n            <artifactId>google-http-client-jackson2</artifactId>\r\n            <version>1.41.4</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springdoc</groupId>\r\n            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>\r\n        </dependency>\r\n\r\n        <!-- HTTP Transport -->\r\n        <dependency>\r\n            <groupId>com.google.http-client</groupId>\r\n            <artifactId>google-http-client</artifactId>\r\n            <version>1.41.4</version>\r\n        </dependency>\r\n        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->\r\n        <dependency>\r\n            <groupId>com.google.code.gson</groupId>\r\n            <artifactId>gson</artifactId>\r\n            <version>2.11.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.google.firebase</groupId>\r\n            <artifactId>firebase-admin</artifactId>\r\n            <version>9.4.1</version>\r\n        </dependency>\r\n <dependency>\r\n            <groupId>com.stripe</groupId>\r\n            <artifactId>stripe-java</artifactId>\r\n            <version>28.1.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-mail</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-web</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-oauth2-client</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-security</artifactId>\r\n        </dependency>\r\n        <!-- JWT library -->\r\n        <dependency>\r\n            <groupId>io.jsonwebtoken</groupId>\r\n            <artifactId>jjwt-api</artifactId>\r\n            <version>0.12.5</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>io.jsonwebtoken</groupId>\r\n            <artifactId>jjwt-impl</artifactId>\r\n            <version>0.12.5</version>\r\n            <scope>runtime</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>io.jsonwebtoken</groupId>\r\n            <artifactId>jjwt-jackson</artifactId>\r\n            <version>0.12.5</version>\r\n            <scope>runtime</scope>\r\n        </dependency>\r\n        <!-- https://mvnrepository.com/artifact/com.twilio.sdk/twilio -->\r\n        <dependency>\r\n            <groupId>com.twilio.sdk</groupId>\r\n            <artifactId>twilio</artifactId>\r\n            <version>10.2.1</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.amazonaws</groupId>\r\n            <artifactId>aws-java-sdk-secretsmanager</artifactId>\r\n            <version>1.12.738</version>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>com.amazonaws</groupId>\r\n            <artifactId>aws-java-sdk-core</artifactId>\r\n            <version>1.12.738</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>software.amazon.awssdk</groupId>\r\n            <artifactId>sdk-core</artifactId>\r\n            <version>2.26.7</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>software.amazon.awssdk</groupId>\r\n            <artifactId>s3</artifactId>\r\n            <version>2.26.7</version>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>org.springdoc</groupId>\r\n            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>\r\n            <version>2.5.0</version>\r\n        </dependency>\r\n       \r\n        <dependency>\r\n            <groupId>com.auth0</groupId>\r\n            <artifactId>java-jwt</artifactId>\r\n            <version>4.5.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.auth0</groupId>\r\n            <artifactId>jwks-rsa</artifactId>\r\n            <version>0.22.1</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-devtools</artifactId>\r\n            <scope>runtime</scope>\r\n            <optional>true</optional>\r\n        </dependency>\r\n            <dependency>\r\n            <groupId>jakarta.validation</groupId>\r\n            <artifactId>jakarta.validation-api</artifactId>\r\n            <version>3.1.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>jakarta.platform</groupId>\r\n            <artifactId>jakarta.jakartaee-api</artifactId>\r\n            <version>11.0.0-M2</version>\r\n            <scope>provided</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>jakarta.servlet</groupId>\r\n            <artifactId>jakarta.servlet-api</artifactId>\r\n            <version>6.1.0-M2</version>\r\n            <scope>provided</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.glassfish</groupId>\r\n            <artifactId>jakarta.el</artifactId>\r\n            <version>4.0.2</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.modelmapper</groupId>\r\n            <artifactId>modelmapper</artifactId>\r\n            <version>3.2.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.mysql</groupId>\r\n            <artifactId>mysql-connector-j</artifactId>\r\n            <scope>runtime</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.projectlombok</groupId>\r\n            <artifactId>lombok</artifactId>\r\n            <optional>true</optional>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-test</artifactId>\r\n            <scope>test</scope>\r\n            <exclusions>\r\n                <exclusion>\r\n                    <artifactId>spring-jcl</artifactId>\r\n                    <groupId>org.springframework</groupId>\r\n                </exclusion>\r\n            </exclusions>\r\n        </dependency>\r\n    </dependencies>\r\n    <dependencyManagement>\r\n        <dependencies>\r\n\r\n        </dependencies>\r\n    </dependencyManagement>\r\n\r\n    <build>\r\n        <plugins>\r\n            <plugin>\r\n                <groupId>org.springframework.boot</groupId>\r\n                <artifactId>spring-boot-maven-plugin</artifactId>\r\n                <configuration>\r\n                    <excludes>\r\n                        <exclude>\r\n                            <groupId>org.projectlombok</groupId>\r\n                            <artifactId>lombok</artifactId>\r\n                        </exclude>\r\n                    </excludes>\r\n                </configuration>\r\n\r\n            </plugin>\r\n\r\n            <plugin>\r\n                <groupId>org.apache.maven.plugins</groupId>\r\n                <artifactId>maven-antrun-plugin</artifactId>\r\n                <executions>\r\n                    <execution>\r\n                        <id>prepare</id>\r\n                        <phase>package</phase>\r\n                        <configuration>\r\n                            <target>\r\n\r\n                                <property name=\"buildName\" value=\"${project.build.finalName}.jar\"/>\r\n\r\n                                <copy todir=\"${project.build.directory}/aws-build/\" overwrite=\"false\">\r\n                                    <fileset file=\"${project.build.directory}/${project.build.finalName}.jar\"/>\r\n                                    <fileset dir=\"./aws\"/>\r\n                                </copy>\r\n\r\n                                <replace file=\"${project.build.directory}/aws-build/Procfile\" token=\"@jarname@\"\r\n                                         value=\"${buildName}\"/>\r\n\r\n                                <zip compress=\"false\"\r\n                                     destfile=\"${project.build.directory}/${project.build.finalName}.jar\"\r\n                                     basedir=\"${project.build.directory}/aws-build\"/>\r\n                                <!--\t\t\t\t\t\t\t\t<delete>-->\r\n                                <!--\t\t\t\t\t\t\t\t\t<fileset dir=\"${project.build.directory}/\" includes=\"*.jar\" />-->\r\n                                <!--\t\t\t\t\t\t\t\t\t<fileset dir=\"${project.build.directory}/\" includes=\"*.jar.original\" />-->\r\n                                <!--\t\t\t\t\t\t\t\t</delete>-->\r\n                                <!--\t\t\t\t\t\t\t\t<copy todir=\"${project.build.directory}/\" overwrite=\"false\">-->\r\n                                <!--\t\t\t\t\t\t\t\t\t<fileset file=\"${project.build.directory}/aws-build/app-to-deploy.jar\"/>-->\r\n                                <!--\t\t\t\t\t\t\t\t\t<fileset dir=\"${project.build.directory}/\" />-->\r\n                                <!--\t\t\t\t\t\t\t\t</copy>-->\r\n                            </target>\r\n\r\n                        </configuration>\r\n                        <goals>\r\n                            <goal>run</goal>\r\n                        </goals>\r\n                    </execution>\r\n                </executions>\r\n            </plugin>\r\n\r\n        </plugins>\r\n    </build>\r\n    <repositories>\r\n        <repository>\r\n            <id>spring-milestones</id>\r\n            <name>Spring Milestones</name>\r\n            <url>https://repo.spring.io/milestone</url>\r\n            <snapshots>\r\n                <enabled>false</enabled>\r\n            </snapshots>\r\n        </repository>\r\n        <repository>\r\n            <id>central</id>\r\n            <name>Maven Central</name>\r\n            <url>https://repo1.maven.org/maven2/</url>\r\n        </repository>\r\n        <repository>\r\n            <id>spring-snapshots</id>\r\n            <name>Spring Snapshots</name>\r\n            <url>https://repo.spring.io/snapshot</url>\r\n            <releases>\r\n                <enabled>false</enabled>\r\n            </releases>\r\n        </repository>\r\n    </repositories>\r\n    <pluginRepositories>\r\n        <pluginRepository>\r\n            <id>spring-milestones</id>\r\n            <name>Spring Milestones</name>\r\n            <url>https://repo.spring.io/milestone</url>\r\n            <snapshots>\r\n                <enabled>false</enabled>\r\n            </snapshots>\r\n        </pluginRepository>\r\n        <pluginRepository>\r\n            <id>spring-snapshots</id>\r\n            <name>Spring Snapshots</name>\r\n            <url>https://repo.spring.io/snapshot</url>\r\n            <releases>\r\n                <enabled>false</enabled>\r\n            </releases>\r\n        </pluginRepository>\r\n    </pluginRepositories>\r\n\r\n</project>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/pom.xml b/pom.xml
--- a/pom.xml	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/pom.xml	(date 1748427485770)
@@ -104,6 +104,10 @@
         </dependency>
         <dependency>
             <groupId>org.springframework.boot</groupId>
+            <artifactId>spring-boot-starter-websocket</artifactId>
+        </dependency>
+        <dependency>
+            <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-oauth2-client</artifactId>
         </dependency>
         <dependency>
Index: src/main/java/com/job/jobportal/service/MessagingService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.job.jobportal.service;\r\n\r\nimport java.util.List;\r\n\r\nimport com.job.jobportal.response.BadRequestException;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.data.domain.Page;\r\nimport org.springframework.data.domain.Pageable;\r\nimport org.springframework.stereotype.Service;\r\nimport org.springframework.transaction.annotation.Transactional;\r\n\r\nimport com.job.jobportal.model.Conversation;\r\nimport com.job.jobportal.model.JobApplication;\r\nimport com.job.jobportal.model.Message;\r\nimport com.job.jobportal.model.Registereduser;\r\nimport com.job.jobportal.model.Roles;\r\nimport com.job.jobportal.repository.ConversationRepository;\r\nimport com.job.jobportal.repository.JobApplicationRepository;\r\nimport com.job.jobportal.repository.MessageRepository;\r\nimport com.job.jobportal.repository.RegisteruserRepository;\r\n\r\n@Service\r\npublic class MessagingService {\r\n    @Autowired\r\n    private ConversationRepository conversationRepository;\r\n\r\n    @Autowired\r\n    private MessageRepository messageRepository;\r\n\r\n    @Autowired\r\n    private RegisteruserRepository userRepository;\r\n\r\n    @Autowired\r\n    private JobApplicationRepository jobApplicationRepository;\r\n\r\n    @Transactional\r\n    public List<Conversation> getConversationsOfUser(Registereduser user) {\r\n        List<Conversation> conversations = conversationRepository.findByAuthorOrRecipientWithRelationships(user);\r\n\r\n        for (Conversation conversation : conversations) {\r\n            if (conversation.getMessages() != null) {\r\n                conversation.getMessages().size();\r\n            }\r\n        }\r\n\r\n        return conversations;\r\n    }\r\n\r\n    @Transactional\r\n    public Conversation getConversation(Registereduser user, Long conversationId) {\r\n        Conversation conversation = conversationRepository.findByIdWithRelationships(conversationId)\r\n                .orElseThrow(() -> new BadRequestException(\"Conversation not found\"));\r\n\r\n        if (!conversation.getAuthor().getUserid().equals(user.getUserid())\r\n                && !conversation.getRecipient().getUserid().equals(user.getUserid())) {\r\n            throw new BadRequestException(\"User not authorized to view conversation\");\r\n        }\r\n\r\n        if (conversation.getMessages() != null) {\r\n            conversation.getMessages().size();\r\n        }\r\n\r\n        return conversation;\r\n    }\r\n\r\n    @Transactional\r\n    public Page<Message> getConversationMessages(Long conversationId, Pageable pageable) {\r\n        return messageRepository.findByConversationIdWithRelationshipsOrderByCreatedAtDesc(conversationId, pageable);\r\n    }\r\n\r\n    @Transactional\r\n    public Conversation createConversationAndAddMessage(Registereduser sender, Long receiverId, String content,\r\n            Long jobApplicationId) {\r\n        Registereduser receiver = userRepository.findById(receiverId)\r\n                .orElseThrow(() -> new BadRequestException(\"Receiver not found\"));\r\n\r\n        validateUserRoles(sender, receiver);\r\n\r\n        conversationRepository.findByAuthorAndRecipient(sender, receiver).ifPresent(\r\n                conversation -> {\r\n                    throw new BadRequestException(\r\n                            \"Conversation already exists, use the conversation id to send messages.\");\r\n                });\r\n\r\n        conversationRepository.findByAuthorAndRecipient(receiver, sender).ifPresent(\r\n                conversation -> {\r\n                    throw new BadRequestException(\r\n                            \"Conversation already exists, use the conversation id to send messages.\");\r\n                });\r\n\r\n        Conversation conversation;\r\n\r\n        if (jobApplicationId != null) {\r\n            try {\r\n                JobApplication jobApplication = jobApplicationRepository.findByIdWithAllAssociations(jobApplicationId)\r\n                        .orElseThrow(() -> new BadRequestException(\"Job application not found\"));\r\n\r\n                if (jobApplication.getJobPost() != null) {\r\n                    jobApplication.getJobPost().getJobTitle();\r\n                    jobApplication.getJobPost().getJobId();\r\n                }\r\n\r\n                if (jobApplication.getCompanyProfile() != null) {\r\n                    jobApplication.getCompanyProfile().getCompanyName();\r\n                }\r\n\r\n                conversation = new Conversation(sender, receiver, jobApplication);\r\n                conversation = conversationRepository.save(conversation);\r\n\r\n                conversation = conversationRepository.findByIdWithRelationships(conversation.getId())\r\n                        .orElseThrow(() -> new BadRequestException(\"Conversation not found after creation\"));\r\n            } catch (Exception e) {\r\n                throw new BadRequestException(\"Error creating conversation with job application: \" + e.getMessage());\r\n            }\r\n        } else {\r\n            conversation = conversationRepository.save(new Conversation(sender, receiver));\r\n        }\r\n\r\n        Message message = new Message(sender, receiver, conversation, content);\r\n        messageRepository.save(message);\r\n        conversation.getMessages().add(message);\r\n\r\n        return conversation;\r\n    }\r\n\r\n    @Transactional\r\n    public Message addMessageToConversation(Long conversationId, Registereduser sender, Long receiverId,\r\n            String content) {\r\n        Registereduser receiver = userRepository.findById(receiverId)\r\n                .orElseThrow(() -> new BadRequestException(\"Receiver not found\"));\r\n\r\n        Conversation conversation = conversationRepository.findByIdWithRelationships(conversationId)\r\n                .orElseThrow(() -> new BadRequestException(\"Conversation not found\"));\r\n\r\n        if (!conversation.getAuthor().getUserid().equals(sender.getUserid())\r\n                && !conversation.getRecipient().getUserid().equals(sender.getUserid())) {\r\n            throw new BadRequestException(\"User not authorized to send message to this conversation\");\r\n        }\r\n\r\n        if (!conversation.getAuthor().getUserid().equals(receiver.getUserid())\r\n                && !conversation.getRecipient().getUserid().equals(receiver.getUserid())) {\r\n            throw new BadRequestException(\"Receiver is not part of this conversation\");\r\n        }\r\n\r\n        Message message = new Message(sender, receiver, conversation, content);\r\n        messageRepository.save(message);\r\n        conversation.getMessages().add(message);\r\n\r\n        return message;\r\n    }\r\n\r\n    @Transactional\r\n    public void markMessageAsRead(Registereduser user, Long messageId) {\r\n        Message message = messageRepository.findById(messageId)\r\n                .orElseThrow(() -> new BadRequestException(\"Message not found\"));\r\n\r\n        if (!message.getReceiver().getUserid().equals(user.getUserid())) {\r\n            throw new BadRequestException(\"User not authorized to mark message as read\");\r\n        }\r\n\r\n        if (!message.getIsRead()) {\r\n            message.setIsRead(true);\r\n            messageRepository.save(message);\r\n        }\r\n    }\r\n\r\n    @Transactional(readOnly = true)\r\n    public int countUnreadMessagesForUser(Registereduser user) {\r\n        return messageRepository.countUnreadMessagesForUser(user);\r\n    }\r\n\r\n    @Transactional\r\n    public List<Conversation> getConversationsByJobPostId(Long jobId) {\r\n        List<Conversation> conversations = conversationRepository.findByJobApplicationJobPostId(jobId);\r\n\r\n        for (Conversation conversation : conversations) {\r\n            if (conversation.getMessages() != null) {\r\n                conversation.getMessages().size();\r\n            }\r\n        }\r\n\r\n        return conversations;\r\n    }\r\n\r\n    @Transactional\r\n    public List<Conversation> getConversationsByJobApplicationId(Long applicationId) {\r\n        List<Conversation> conversations = conversationRepository.findByJobApplicationId(applicationId);\r\n\r\n        for (Conversation conversation : conversations) {\r\n            if (conversation.getMessages() != null) {\r\n                conversation.getMessages().size();\r\n            }\r\n        }\r\n\r\n        return conversations;\r\n    }\r\n\r\n    private void validateUserRoles(Registereduser sender, Registereduser receiver) {\r\n        boolean senderIsRecruiter = hasRole(sender, \"RECRUITER\");\r\n        boolean receiverIsCandidate = hasRole(receiver, \"CANDIDATE\");\r\n\r\n        boolean senderIsCandidate = hasRole(sender, \"CANDIDATE\");\r\n        boolean receiverIsRecruiter = hasRole(receiver, \"RECRUITER\");\r\n\r\n        if (!((senderIsRecruiter && receiverIsCandidate) ||\r\n                (senderIsCandidate && receiverIsRecruiter))) {\r\n            throw new BadRequestException(\"Messaging is only allowed between recruiters and candidates\");\r\n        }\r\n    }\r\n\r\n    private boolean hasRole(Registereduser user, String roleName) {\r\n        return user.getRoles().stream()\r\n                .map(Roles::getRolename)\r\n                .anyMatch(role -> role.equals(roleName));\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/job/jobportal/service/MessagingService.java b/src/main/java/com/job/jobportal/service/MessagingService.java
--- a/src/main/java/com/job/jobportal/service/MessagingService.java	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/src/main/java/com/job/jobportal/service/MessagingService.java	(date 1748427762427)
@@ -6,6 +6,7 @@
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.data.domain.Page;
 import org.springframework.data.domain.Pageable;
+import org.springframework.messaging.simp.SimpMessagingTemplate;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
@@ -33,6 +34,9 @@
     @Autowired
     private JobApplicationRepository jobApplicationRepository;
 
+    @Autowired
+    private SimpMessagingTemplate messagingTemplate;
+
     @Transactional
     public List<Conversation> getConversationsOfUser(Registereduser user) {
         List<Conversation> conversations = conversationRepository.findByAuthorOrRecipientWithRelationships(user);
@@ -120,6 +124,9 @@
         messageRepository.save(message);
         conversation.getMessages().add(message);
 
+        messagingTemplate.convertAndSend("/topic/users/" + sender.getUserid() + "/conversations", conversation);
+        messagingTemplate.convertAndSend("/topic/users/" + receiver.getUserid() + "/conversations", conversation);
+
         return conversation;
     }
 
@@ -146,6 +153,10 @@
         messageRepository.save(message);
         conversation.getMessages().add(message);
 
+        messagingTemplate.convertAndSend("/topic/conversations/" + conversationId + "/messages", message);
+        messagingTemplate.convertAndSend("/topic/users/" + sender.getUserid() + "/conversations", conversation);
+        messagingTemplate.convertAndSend("/topic/users/" + receiver.getUserid() + "/conversations", conversation);
+
         return message;
     }
 
@@ -161,6 +172,8 @@
         if (!message.getIsRead()) {
             message.setIsRead(true);
             messageRepository.save(message);
+
+            messagingTemplate.convertAndSend("/topic/conversations/" + message.getConversation().getId() + "/messages", message);
         }
     }
 
Index: target/classes/messages.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>msg.course_created_success=course created successfully\r\nmsg.course_get_success=course get successfully\r\nmsg.course_update_success=course updated successfully\r\nmsg.course_update_failed=course updated failed\r\nmsg.course_deleted_success=course deleted successfully\r\nmsg.course_deleted_warning=action failed . Only course which has draft status can be deleted \r\nmsg.course_publish_success=course published successfully\r\nmsg.course_draft_success=course changed to draft successfully\r\nmsg.topic_move_success=topic moved successfully\r\nmsg.quiz_created_success=quiz created successfully\r\nmsg.quiz_get_success=quiz get successfully\r\nmsg.quiz_update_success=quiz updated successfully\r\nmsg.quiz_update_failed=quiz updated failed\r\nmsg.quiz_deleted_success=quiz deleted successfully\r\nmsg.quiz_publish_success=quiz published successfully\r\nmsg.quiz_draft_success=quiz changed to draft successfully\r\nmsg.question_created_success=question created successfully\r\nmsg.question_get_success=question get successfully\r\nmsg.question_update_success=question updated successfully\r\nmsg.question_deleted_success=question deleted successfully\r\nmsg.question_update_failed=question updated failed\r\nmsg.addtocart_get_success=addtocart get successfull\r\nmsg.addtocart_added_success=addtocart Added successFully\r\nmsg.addtocart_deleted_success=addtocart Removed successFully\r\nmsg.answer_saved_success=answer saved successfully\r\nmsg.answer_updated_success=answer updated successfully\r\nmsg.login_success=login successful\r\nmsg.user_inactive=user is inactive for long time or blocked by administrator please contact us \r\nmsg.user_created_success=user created  successful\r\nmsg.user_social_created_failed=user registration failed \r\nmsg.userdetails_changed_success=User Details changed successfully\r\nmsg.userdetails_get_success=User Details get successfully\r\nmsg.resource_added_success=Resource added successfully\r\nmsg.resource_get_success=Resource get successfully\r\nmsg.resource_deleted_success=Resource deleted successfully\r\nmsg.thumbnail_added_success=Thumbnail added successfully\r\n#marketing\r\nmsg.email_sent_success=email sent successfully\r\nmsg.SMS_sent_success=SMS send successfully\r\nmsg.emailtemplate_get_success=email template get successfully\r\nmsg.emailtemplate_added_success=email template added successfully\r\nmsg.emailtemplate_updated_success=email template updated successfully\r\nmsg.emailtemplate_deleted_success=email template deleted successfully\r\nmsg.SMStemplate_get_success=SMS template get successfully\r\nmsg.SMStemplate_added_success=SMS template added successfully\r\nmsg.SMStemplate_updated_success=SMS template updated successfully\r\nmsg.SMStemplate_deleted_success=SMS template deleted successfully\r\nmsg.blog_added_success=Blog added successfully\r\nmsg.blog_updated_success=Blog updated successfully\r\nmsg.blog_deleted_success=Blog deleted successfully\r\nmsg.blog_get_success=Blog get successfully\r\nmsg.blogdetails_get_success=Blog details get successfully\r\n#event\r\nmsg.event_added_success=Event added successfully\r\nmsg.event_updated_success=Event updated successfully\r\nmsg.event_deleted_success=Event deleted successfully\r\nmsg.event_get_success=Event get successfully\r\nmsg.eventdetails_get_success=Event details get successfully\r\n#seopages\r\nmsg.seopages_added_success=SeoPages added successfully\r\nmsg.seopages_updated_success=SeoPages updated successfully\r\nmsg.seopages_deleted_success=SeoPages deleted successfully\r\nmsg.seopages_get_success=SeoPages get successfully\r\nmsg.seopagesdetails_get_success=SeoPages details get successfully\r\n\r\nmsg.otp_sent_success=OTP sent successfully\r\nmsg.otp_verify_success=OTP verified successfully\r\nmsg.otp_resend_success=OTP Re-sent successfully\r\nmsg.something_went_wrong=Something went wrong\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/target/classes/messages.properties b/target/classes/messages.properties
--- a/target/classes/messages.properties	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/target/classes/messages.properties	(date 1747380341425)
@@ -3,7 +3,7 @@
 msg.course_update_success=course updated successfully
 msg.course_update_failed=course updated failed
 msg.course_deleted_success=course deleted successfully
-msg.course_deleted_warning=action failed . Only course which has draft status can be deleted 
+msg.course_deleted_warning=action failed . Only course which has draft status can be deleted
 msg.course_publish_success=course published successfully
 msg.course_draft_success=course changed to draft successfully
 msg.topic_move_success=topic moved successfully
@@ -25,19 +25,31 @@
 msg.answer_saved_success=answer saved successfully
 msg.answer_updated_success=answer updated successfully
 msg.login_success=login successful
-msg.user_inactive=user is inactive for long time or blocked by administrator please contact us 
+
+msg.role_mismatch=role mismatch
+msg.user_inactive=user is inactive for long time or blocked by administrator please contact us
 msg.user_created_success=user created  successful
-msg.user_social_created_failed=user registration failed 
-msg.userdetails_changed_success=User Details changed successfully
+msg.user_social_created_failed=user registration failed
+
 msg.userdetails_get_success=User Details get successfully
+
+msg.login_failed=User Email or password is wrong
+msg.user_not_found=User not found
+
+
+
+
 msg.resource_added_success=Resource added successfully
 msg.resource_get_success=Resource get successfully
 msg.resource_deleted_success=Resource deleted successfully
 msg.thumbnail_added_success=Thumbnail added successfully
 #marketing
 msg.email_sent_success=email sent successfully
+msg.bulk_email_sent_success=Bulk email sent successfully to all selected applicants
 msg.SMS_sent_success=SMS send successfully
 msg.emailtemplate_get_success=email template get successfully
+msg.keywords_required=At least one keyword is required
+msg.invalid_keywords_format=Keywords must contain only letters, numbers, and spaces
 msg.emailtemplate_added_success=email template added successfully
 msg.emailtemplate_updated_success=email template updated successfully
 msg.emailtemplate_deleted_success=email template deleted successfully
@@ -49,7 +61,7 @@
 msg.blog_updated_success=Blog updated successfully
 msg.blog_deleted_success=Blog deleted successfully
 msg.blog_get_success=Blog get successfully
-msg.blogdetails_get_success=Blog details get successfully
+msg.blog_details_get_success=Blog details get successfully
 #event
 msg.event_added_success=Event added successfully
 msg.event_updated_success=Event updated successfully
@@ -65,6 +77,242 @@
 
 msg.otp_sent_success=OTP sent successfully
 msg.otp_verify_success=OTP verified successfully
+msg.otp_verify_failed=OTP Verification Failed
 msg.otp_resend_success=OTP Re-sent successfully
 msg.something_went_wrong=Something went wrong
 
+# Company Profile Messages
+msg.company_profile_created=Company profile added successfully
+msg.company_profile_updated=Company profile updated successfully
+msg.company_profile_fetched=Company profile details fetched successfully
+msg.company_profiles_active_fetched=Non-archived and active companies fetched successfully
+msg.company_profiles_archived_fetched=Archived companies fetched successfully
+msg.company_profile_archived=Company archived successfully
+msg.company_profile_restored=Company restored successfully
+msg.company_profile_deleted=Company deleted successfully
+msg.company_profile_activated=Company activated successfully
+msg.company_profile_deactivated=Company deactivated successfully
+msg.no_company_profile=No company profile found
+
+# Request
+msg.request_failed=Request failed
+
+#dashboard
+msg.dashboard_details_get_success=DashBoard Details get successfully
+
+#ticket
+msg.ticket_raised_success=Ticket Raised successfully
+msg.feedback_submitted_success=Feedback submitted successfully
+msg.feedback_get_success=Feedback get successfully
+
+# notification
+msg.notification_get_all_success=get all notification successfully
+msg.notification_added_success=Notification added successfully
+
+# notes
+msg.dairynotes_details_added_success=DairyNotes Details added successfully
+
+# profileDetails
+msg.profile_details_get_success=Profile Details get successfully
+msg.profile_details_added_success=Profile Details added successfully
+msg.profile_details_updated_success=Profile Details updated successfully
+
+# rating review
+msg.rating_review_get_success=Rating and Review get successfully
+msg.rating_review_delete_success=Rating and Review delete successfully
+msg.rating_review_save_success=Rating and Review save successfully
+
+# password
+msg.password_mismatch=Password does not match
+msg.password_change_success=password changed successfully
+msg.password_add_success=password added successfully
+msg.userdetails_changed_success=User Details changed successfully
+
+# job posts
+msg.job_created=Job created successfully
+msg.jobs_fetched=Jobs fetched successfully
+msg.job_fetched=Job fetched successfully
+msg.job_updated=Job updated successfully
+msg.job_deleted=Job deleted successfully
+msg.job_status_updated=Job status updated successfully
+msg.job_title_required=Job title is required
+msg.job_title_id_required=jobTitleId is required
+msg.valid_contact_email=Valid contact email is required
+msg.min_salary_invalid=Min salary cannot be greater than max salary
+msg.company_profile_required=Company profile ID is required
+msg.user_required=User ID is required
+msg.job_not_found=Job not found
+msg.company_not_found=Company not found
+msg.invalid_job_subcategory=Invalid job subcategory
+msg.invalid_job_subcategory_name=Invalid job subcategory name
+msg.subcategory_not_match_category=The selected subcategory does not belong to the selected category
+msg.category_id_required=categoryId is required
+msg.subcategory_id_required=subcategoryId is required
+msg.city_id_required=cityId is required
+
+# candidate profile
+msg.candidate_profile_fetched=Candidate profile fetched successfully
+msg.candidates_fetched=Candidates fetched successfully
+msg.candidate_profile_created=Candidate profile created successfully
+msg.candidate_profile_updated=Candidate profile updated successfully
+msg.candidate_activated=Candidate activated successfully
+msg.candidate_deactivated=Candidate deactivated successfully
+
+msg.registered_user_not_found=Registered User Not found
+msg.registered_user_no_linked_company=Company Not Linked To Registered User
+
+# master data
+msg.master_data_fetched=Master data fetched successfully
+msg.master_data_not_found=No master data found for componentTypeIds: {0}
+msg.mapping_error=Error mapping data
+msg.invalid_component_type_ids=Component type IDs must not be null, empty, or contain invalid values
+msg.subsubcategories_fetched=Job sub-subcategories fetched successfully.
+msg.districts_fetched=Districts fetched successfully.
+msg.job_skills_fetched=Job skills fetched successfully.
+
+# resume
+msg.resume.created=Resume created successfully
+msg.resumes.fetched=Resumes fetched successfully
+msg.resume.updated=Resume updated successfully
+msg.resume.deleted=Resume deleted successfully
+msg.resume.title_exists=Resume with this title already exists
+msg.resume.not_found=Resume not found
+msg.education.added=Education entry added successfully
+msg.work_experience.added=Work experience added successfully
+msg.award.added=Award added successfully
+
+# Education
+msg.education.updated=Education entry updated successfully
+msg.education.deleted=Education entry deleted successfully
+msg.education.not_found=Education entry not found
+
+# Work Experience
+msg.work_experience.updated=Work experience updated successfully
+msg.work_experience.deleted=Work experience deleted successfully
+msg.work_experience.not_found=Work experience not found
+
+# Awards
+msg.award.updated=Award updated successfully
+msg.award.deleted=Award deleted successfully
+msg.award.not_found=Award not found
+
+# Validation
+msg.validation.end_date_before_start=End date cannot be before start date
+msg.validation.future_date=Date cannot be in the future
+
+
+# Validation
+msg.validation.title_required=Resume title is required
+msg.validation.institution_required=Institution name is required
+msg.validation.degree_required=Degree name is required
+msg.validation.start_date_required=Start date is required
+msg.validation.job_title_required=Job title is required
+msg.validation.company_required=Company name is required
+msg.validation.work_start_date_required=Work start date is required
+msg.validation.award_title_required=Award title is required
+msg.validation.issuer_required=Award issuer is required
+msg.validation.issue_date_required=Issue date is required
+msg.validation.failed=Validation failed
+
+# Errors
+msg.generic_error=An error occurred
+
+# retry
+msg.retry=Something went wrong while processing your request. Would you like to retry?
+msg.delete_token_generated=Delete confirmation token generated
+
+# job application
+msg.job_application_submit_success=Job application submitted successfully.
+msg.job_application_count_fetch_success=Job application count retrieved successfully.
+msg.job_application_status_update_success=Job application status updated successfully.
+msg.job_application_retrieve_success=Job applications retrieved successfully.
+msg.job_application_has_applied_check_success=Job application status check successful.
+msg.already_applied=You have already applied for this job.
+msg.candidate_profile_not_found=Candidate profile not found.
+msg.job_application_not_found=Job application not found.
+msg.company_profile_not_found=Company profile not found.
+msg.invalid_role=Invalid user role.
+msg.no_permission=You do not have permission to access this resource
+msg.applicant_ids_required=Applicant IDs are required
+msg.email_subject_required=Email subject is required
+msg.email_body_required=Email body is required
+msg.subcategories_fetched=Job subcategories retrieved successfully.
+msg.resource_subcategories_fetched=Resource subcategories retrieved successfully.
+
+# SEO and Programmatic Pages
+msg.categories_fetched=Categories fetched successfully.
+msg.seo_subcategories_fetched=SEO subcategories fetched successfully.
+msg.seo_subsubcategories_fetched=SEO sub-subcategories fetched successfully.
+msg.programmatic_categories_fetched=Programmatic categories fetched successfully.
+msg.programmatic_subcategories_fetched=Programmatic subcategories fetched successfully.
+msg.programmatic_page_created=Programmatic page created successfully.
+msg.programmatic_page_updated=Programmatic page updated successfully.
+msg.programmatic_page_deleted=Programmatic page deleted successfully.
+msg.programmatic_pages_fetched=Programmatic pages fetched successfully.
+msg.programmatic_page_fetched=Programmatic page fetched successfully.
+msg.city_state_combinations_fetched=Locations fetched successfully.
+msg.seo_metadata_generated=SEO metadata generated successfully
+msg.bulk_seo_metadata_generated=Bulk SEO metadata generated successfully
+msg.error_generating_seo_metadata=Error generating SEO metadata
+msg.error_generating_bulk_seo_metadata=Error generating bulk SEO metadata
+
+
+# contactUs
+msg.contact_us_form_success=Thank you for reaching out! Your message has been sent successfully.
+msg.contact_us_form_error=Something went wrong. Please try again later.
+msg.contact_us_name_mandatory=Name is mandatory.
+msg.contact_us_email_mandatory=Email is mandatory.
+msg.contact_us_email_valid=Email should be valid.
+msg.contact_us_message_mandatory=Message is mandatory.
+
+# s3 bucket
+msg.file_cannot_be_empty=File cannot be empty
+msg.file_uploaded_success=File uploaded successfully
+msg.file_processing_error=File processing error
+msg.file_storage_error=File storage error
+msg.file_deleted=File deleted successfully
+error.file_deletion_failed=Failed to delete file from storage
+
+# auth login
+msg.email_registered_different_role=Email is already registered under a different role.
+msg.email_registered_as_admin=This Email ID is already registered as an Admin; please use the correct login.
+msg.email_registered_as_manager=This Email ID is already registered as a Manager; please use the correct login.
+msg.email_registered_as_staff=This Email ID is already registered as Staff; please use the correct login.
+msg.email_registered_as_recruiter=This Email ID is already registered as a Recruiter; please use the correct login.
+msg.email_registered_as_candidate=This Email ID is already registered as a Candidate; please use the correct login.
+msg.email_registered_as_user=This Email ID is already registered as a User; please use the correct login.
+msg.email_registered_under_different_role=This Email ID is already registered under a different role; please use the correct login.
+msg.success=Operation completed successfully
+msg.mobile_number_required=Mobile number is required
+
+msg.invalid_specialisms=Invalid specialisms provided.
+
+
+msg.checkout.session.created=Checkout session created successfully
+msg.subscription.details.fetched=Subscription details retrieved successfully
+msg.subscription.cancelled=Subscription cancelled successfully
+
+msg.salary_currency_required=Salary currency is required
+msg.pay_type_required=Pay type is required
+msg.invalid_salary_currency=Invalid salary currency
+msg.invalid_pay_type=Invalid pay type
+msg.min_salary_required=Minimum salary is required
+msg.max_salary_required=Maximum salary is required
+
+msg.invalid_deadline_range=Application deadline must be between job opening date and 30 days after
+
+msg.responsibilities_and_benefits_required=Responsibilities and benefits are required
+
+# job applicants
+msg.job_applicants_retrieve_success=Job applicants retrieved successfully.
+msg.company_profile_incomplete=Please complete your company profile to view applicants.
+msg.job_not_owned=You do not have permission to access this job.
+msg.invalid_status=Invalid application status ID provided.
+msg.invalid_job_category_name=Invalid job category name
+msg.jobs_with_applicants_retrieve_success=Jobs with applicants retrieved successfully.
+msg.no_search_parameters=At least one search parameter is required for programmatic page search.
+msg.no_programmatic_pages_found=No programmatic pages found.
+
+msg.job_post_limit_reached=You have reached your job post limit for this month. Please upgrade your subscription or wait until the next month.
+
+
Index: target/classes/schema.sql
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>INSERT  INTO roles(roleName) VALUES('ADMIN'),('MANAGER'),('STAFF'),('RECRUITER'),('CANDIDATE')\r\n$$\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/target/classes/schema.sql b/target/classes/schema.sql
--- a/target/classes/schema.sql	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/target/classes/schema.sql	(date *************)
@@ -1,2 +1,985 @@
-INSERT  INTO roles(roleName) VALUES('ADMIN'),('MANAGER'),('STAFF'),('RECRUITER'),('CANDIDATE')
+INSERT INTO account_details (is_active, is_delete_scheduled, is_premium_account)
+VALUES (1, 0, 1), (1, 0, 1)$$
+
+INSERT INTO roles (roleName)
+VALUES ('ADMIN'), ('MANAGER'), ('STAFF'), ('RECRUITER'), ('CANDIDATE'), ('USER')$$
+
+INSERT INTO registereduser (has_password, has_company_profile_id, has_candidate_profile, userid, email, password, confirmpassword, username, account_details_id, provider)
+VALUES
+(1, false, false, 1, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'superadmin', 1, 'local'),
+(1, false, false, 2, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'marketing', 2, 'local')
+$$
+
+INSERT INTO users_roles (user_id, role_id)
+VALUES
+(1, 1),
+(2, 3)$$
+
+-- Add payment methods
+INSERT INTO payment_method (id, name, stripe_method_type, description, active) VALUES
+(1, 'Credit/Debit Card', 'card', 'Pay with credit or debit card', true),
+(2, 'Amazon Pay', 'amazon_pay', 'Pay with Amazon Pay', true),
+(3, 'PayPal', 'paypal', 'Pay with PayPal', true)$$
+
+INSERT INTO component_type (id, name) VALUES
+(1, 'skills'),
+(2, 'departments'),
+(3, 'locations'),
+(4, 'career_level'),
+(5, 'experience'),
+(6, 'manage_jobs'),
+(7, 'all_applications'),
+(8, 'qualifications'),
+(9, 'nature_of_business'),
+(10, 'company_size'),
+(11, 'employee_count_range'),
+(12, 'job_categories'),
+(13, 'job_type'),
+(14, 'salary_currency'),
+(15, 'pay_type'),
+(16, 'application_status'),
+(17, 'job_subcategories'),
+(18, 'resources'),
+(19, 'tools'),
+(20, 'resource_subcategories'),
+(21, 'seo_categories'),
+(22, 'seo_subcategories'),
+(23, 'job_subsubcategories'),
+(24, 'districts'),
+(25, 'seo_subsubcategories'),
+(26, 'permission_types'),
+(27, 'subscription_plan_permissions')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(1, 1, 'A&E'),
+(1, 2, 'Cardiology'),
+(1, 3, 'Medical'),
+(1, 4, 'General wards'),
+(1, 5, 'IT Consultant'),
+(1, 6, 'ITU/HDU'),
+(1, 7, 'Obs & Gynae'),
+(1, 8, 'Surgical'),
+(1, 9, 'Mental health'),
+(1, 10, 'Software Engineer'),
+(1, 11, 'Theatres'),
+(1, 12, 'Midwifery'),
+(1, 13, 'Orthopaedics'),
+(1, 14, 'Community'),
+(1, 15, 'Developer'),
+(1, 16, 'Endoscopy'),
+(1, 17, 'Paediatrics'),
+(1, 18, 'ODP'),
+(1, 19, 'General Practitioner'),
+(1, 20, 'HR Admin'),
+(1, 21, 'Chemotherapy'),
+(1, 22, 'Radiology'),
+(1, 23, 'Urology'),
+(1, 24, 'Nurse practitioner'),
+(1, 25, 'Testing'),
+(1, 26, 'Neonatal/PICU'),
+(1, 27, 'Palliative'),
+(1, 28, 'Dialysis'),
+(1, 29, 'ENT'),
+(1, 30, 'Others')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(2, 1, 'HR'),
+(2, 2, 'Finance'),
+(2, 3, 'Engineering'),
+(2, 4, 'Health Care'),
+(2, 5, 'Social Care')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(3, 1, 'New York'),
+(3, 2, 'London'),
+(3, 3, 'Mumbai')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(4, 1, 'Entry level'),
+(4, 2, 'Junior level'),
+(4, 3, 'Mid-Level'),
+(4, 4, 'Senior-Level'),
+(4, 5, 'Executive/Management'),
+(4, 6, 'Others')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(5, 1, 'No experience'),
+(5, 2, 'Less than 1 year'),
+(5, 3, '1-3 years'),
+(5, 4, '3-5 years'),
+(5, 5, '5-10 years'),
+(5, 6, '10-15 years'),
+(5, 7, '15+ years')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(6, 1, 'Today'),
+(6, 2, 'Last 7 days'),
+(6, 3, 'Last 30 days'),
+(6, 4, 'Last 45 days'),
+(6, 5, 'Last 60 days'),
+(6, 6, 'Last 90 days'),
+(6, 7, 'Last 6 months'),
+(6, 8, 'Last 12 months'),
+(6, 9, 'Last 24 months'),
+(6, 10, 'Last 5 years'),
+(6, 11, 'Last 7 years')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(7, 1, 'Today'),
+(7, 2, 'Last 7 days'),
+(7, 3, 'Last 30 days'),
+(7, 4, 'Last 45 days'),
+(7, 5, 'Last 60 days'),
+(7, 6, 'Last 90 days'),
+(7, 7, 'Last 6 months'),
+(7, 8, 'Last 12 months'),
+(7, 9, 'Last 24 months'),
+(7, 10, 'Last 5 years'),
+(7, 11, 'Last 7 years')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(8, 1, 'GCSE/A level'),
+(8, 2, 'Diploma'),
+(8, 3, 'Degree'),
+(8, 4, 'Post graduate'),
+(8, 5, 'Doctorate'),
+(8, 6, 'Certificate course'),
+(8, 7, 'Others')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(9, 1, 'Accounting & Finance'),
+(9, 2, 'Education'),
+(9, 3, 'Engineering'),
+(9, 4, 'Health care'),
+(9, 5, 'Information technology'),
+(9, 6, 'Social care')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(10, 1, '1-50'),
+(10, 2, '101 - 200'),
+(10, 3, '201 - 300'),
+(10, 4, '301 - 400'),
+(10, 5, '401 - 500'),
+(10, 6, '501 - 750'),
+(10, 7, '51 - 100'),
+(10, 8, '751 - 1000'),
+(10, 9, '1000+')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(11, 1, '1-10'),
+(11, 2, '11-50'),
+(11, 3, '51-100'),
+(11, 4, '101-250'),
+(11, 5, '251-500'),
+(11, 6, '501-750'),
+(11, 7, '751-1000'),
+(11, 8, '1000+')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(12, 1, 'Allied health professionals'),
+(12, 2, 'Ambulance service team'),
+(12, 3, 'Dental team'),
+(12, 4, 'Doctors'),
+(12, 5, 'Estates and facilities'),
+(12, 6, 'Health informatics'),
+(12, 7, 'Healthcare science'),
+(12, 8, 'Healthcare support worker'),
+(12, 9, 'Management'),
+(12, 10, 'Medical associate professions'),
+(12, 11, 'Midwifery'),
+(12, 12, 'Nursing'),
+(12, 13, 'Pharmacy'),
+(12, 14, 'Psychological professions'),
+(12, 15, 'Public health'),
+(12, 16, 'Wider healthcare team')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(13, 1, 'Full-time'),
+(13, 2, 'Part-time'),
+(13, 3, 'Permanent'),
+(13, 4, 'Contract'),
+(13, 5, 'Temporary'),
+(13, 6, 'Training'),
+(13, 7, 'Freelancer'),
+(13, 8, 'Volunteer')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(14, 1, 'GBP'),
+(14, 2, 'USD'),
+(14, 3, 'EUR'),
+(14, 4, 'AUD'),
+(14, 5, 'CAD'),
+(14, 6, 'INR')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(15, 1, 'Hourly'),
+(15, 2, 'Weekly'),
+(15, 3, 'Monthly'),
+(15, 4, 'Yearly')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(16, 1, 'Applied'),
+(16, 2, 'Shortlisted'),
+(16, 3, 'Interview'),
+(16, 4, 'Hired'),
+(16, 5, 'Rejected')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 1, 'Art therapist/art psychotherapist|1'),
+(17, 2, 'Diagnostic radiographer|1'),
+(17, 3, 'Dietitian|1'),
+(17, 4, 'Dramatherapist|1'),
+(17, 5, 'Music therapist|1'),
+(17, 6, 'Occupational therapist|1'),
+(17, 7, 'Operating department practitioner|1'),
+(17, 8, 'Orthoptist|1'),
+(17, 9, 'Osteopath|1'),
+(17, 10, 'Paramedic|1'),
+(17, 11, 'Physiotherapist|1'),
+(17, 12, 'Podiatrist|1'),
+(17, 13, 'Prosthetist/orthotist|1'),
+(17, 14, 'Speech and language therapist|1'),
+(17, 15, 'Therapeutic radiographer|1')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 16, 'Ambulance care assistant and Patient Transport Service (PTS) driver|2'),
+(17, 17, 'Call handler/emergency medical dispatcher|2'),
+(17, 18, 'Emergency care assistant|2'),
+(17, 19, 'Emergency medical technician|2'),
+(17, 20, 'Experienced paramedic|2'),
+(17, 21, 'Patient transport service (PTS) call handler|2')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 22, 'Dental|3'),
+(17, 23, 'Dental nurse|3'),
+(17, 24, 'Dental technician/ dental technologist|3'),
+(17, 25, 'Dental Therapist|3'),
+(17, 26, 'Dentist|3')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 27, 'Anesthesia|4'),
+(17, 28, 'Clinical oncology|4'),
+(17, 29, 'Clinical radiology|4'),
+(17, 30, 'Community sexual and reproductive health|4'),
+(17, 31, 'Emergency medicine|4'),
+(17, 32, 'General practitioner|4'),
+(17, 33, 'Intensive care medicine|4'),
+(17, 34, 'Obstetrics and gynaecology|4'),
+(17, 35, 'Occupational medicine|4'),
+(17, 36, 'Ophthalmology|4'),
+(17, 37, 'Medicine Doctor|4'),
+(17, 38, 'Pathology Doctor|4'),
+(17, 39, 'Psychiatry Doctor|4'),
+(17, 40, 'Surgery Doctor|4'),
+(17, 41, 'Paediatrics|4')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 42, 'Domestic services|5'),
+(17, 43, 'Estates services|5'),
+(17, 44, 'Support services|5')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 45, 'Clinical informatics|6'),
+(17, 46, 'Education and training roles|6'),
+(17, 47, 'Health records and patient administration|6'),
+(17, 48, 'Information and communication technology|6'),
+(17, 49, 'Information management staff|6'),
+(17, 50, 'Knowledge and library services|6'),
+(17, 51, 'Project and programme management|6')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 52, 'Clinical bioinformatics|7'),
+(17, 53, 'Life sciences|7'),
+(17, 54, 'Physical sciences and biomedical engineering|7'),
+(17, 55, 'Physiological sciences|7')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 56, 'Dietetic assistant|8'),
+(17, 57, 'Healthcare assistant|8'),
+(17, 58, 'Healthcare support worker|8'),
+(17, 59, 'Mammography associate|8'),
+(17, 60, 'Maternity support worker|8'),
+(17, 61, 'Occupational therapy support worker|8'),
+(17, 62, 'Podiatry assistant|8'),
+(17, 63, 'Prosthetic technician|8'),
+(17, 64, 'Radiography assistants and imaging support workers|8'),
+(17, 65, 'Speech and language therapy assistant|8')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 66, 'Clinical manager|9'),
+(17, 67, 'Estates manager|9'),
+(17, 68, 'Finance manager|9'),
+(17, 69, 'General management|9'),
+(17, 70, 'Human resources (HR) manager|9'),
+(17, 71, 'Operational management|9'),
+(17, 72, 'Practice manager|9'),
+(17, 73, 'Project manager|9')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 74, 'Anaesthesia associate|10'),
+(17, 75, 'Physician associate|10'),
+(17, 76, 'Surgical care practitioner|10')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 77, 'Midwife|11')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 78, 'Adult nurse|12'),
+(17, 79, 'Childrens nurse|12'),
+(17, 80, 'District nurse|12'),
+(17, 81, 'General practice nurse|12'),
+(17, 82, 'Learning disability nurse|12'),
+(17, 83, 'Mental health nurse|12'),
+(17, 84, 'Nursing associate|12'),
+(17, 85, 'Prison nurse|12'),
+(17, 86, 'Theatre nurse|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 87, 'Pharmacist|13'),
+(17, 88, 'Pharmacy assistant|13'),
+(17, 89, 'Pharmacy technician|13')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 90, 'Adult psychotherapist|14'),
+(17, 91, 'Assistant psychologist|14'),
+(17, 92, 'CBT therapist|14'),
+(17, 93, 'Child and adolescent psychotherapist|14'),
+(17, 94, 'Childrens wellbeing practitioner|14'),
+(17, 95, 'Clinical associate in psychology|14'),
+(17, 96, 'Clinical psychologist|14'),
+(17, 97, 'Counselling psychologist|14'),
+(17, 98, 'Counsellor|14'),
+(17, 99, 'Education mental health practitioner|14'),
+(17, 100, 'Family and systemic psychotherapist|14'),
+(17, 101, 'Forensic psychologist|14'),
+(17, 102, 'Health psychologist|14'),
+(17, 103, 'High intensity therapist|14'),
+(17, 104, 'Mental health and wellbeing practitioner|14'),
+(17, 105, 'Peer support worker|14'),
+(17, 106, 'Psychological wellbeing practitioner|14'),
+(17, 107, 'Youth intensive psychological practitioner|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 108, 'Director of public health|15'),
+(17, 109, 'Environmental health professional|15'),
+(17, 110, 'Health trainer|15'),
+(17, 111, 'Health visitor|15'),
+(17, 112, 'Occupational health nurse|15'),
+(17, 113, 'Public health academic|15'),
+(17, 114, 'Public health consultants and specialists|15'),
+(17, 115, 'Public health knowledge and intelligence professional|15'),
+(17, 116, 'Public health manager|15'),
+(17, 117, 'Public health nurse|15'),
+(17, 118, 'Public health practitioner|15'),
+(17, 119, 'School nurse|15')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 120, 'Wider healthcare team|16'),
+(17, 121, 'Clinical support staff|16'),
+(17, 122, 'Corporate services|16')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 1, 'CV Writing Tips|11'),
+(20, 2, 'Interview Preparation|11'),
+(20, 3, 'Career Development|11'),
+(20, 4, 'Job Market Trends|11'),
+(20, 5, 'Healthcare Industry News|11')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 6, 'CV Templates|12'),
+(20, 7, 'CV Help & Tips|12'),
+(20, 8, 'Personal Statement|12'),
+(20, 9, 'Interview Questions|12'),
+(20, 10, 'Interview Advice|12'),
+(20, 11, 'Career Development|12'),
+(20, 12, 'Job Applications|12'),
+(20, 13, 'Career Change|12'),
+(20, 14, 'Job Descriptions|12'),
+(20, 15, 'Starting a New Job|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 16, 'Nursing Roles|13'),
+(20, 17, 'Doctor Roles|13'),
+(20, 18, 'Allied Health Roles|13'),
+(20, 19, 'Administrative Roles|13'),
+(20, 20, 'Support Staff Roles|13')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 21, 'Hiring Tips|14'),
+(20, 22, 'Job Posting Guidelines|14'),
+(20, 23, 'Candidate Screening|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(21, 1, 'Blog'),
+(21, 2, 'Career Guide'),
+(21, 3, 'Explore Roles'),
+(21, 4, 'Employer Resource')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 1, 'Tips|1'),
+(22, 2, 'News|1'),
+(22, 3, 'Tutorials|1'),
+(22, 4, 'Case Studies|1')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 5, 'Resume|2'),
+(22, 6, 'Interview|2'),
+(22, 7, 'Job Search|2'),
+(22, 8, 'Career Development|2')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 9, 'Healthcare|3'),
+(22, 10, 'Technology|3'),
+(22, 11, 'Finance|3'),
+(22, 12, 'Education|3')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 13, 'Hiring|4'),
+(22, 14, 'Retention|4'),
+(22, 15, 'Workplace|4'),
+(22, 16, 'Compliance|4')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 24, 'Employer Branding|14'),
+(20, 25, 'Retention Strategies|14')$$
+
+-- add job sub-subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 1, 'Acute internal medicine|37'),
+(23, 2, 'Allergy|37'),
+(23, 3, 'Audiovestibular medicine|37'),
+(23, 4, 'Cardiologist|37'),
+(23, 5, 'Clinical genetics|37'),
+(23, 6, 'Clinical neurophysiology|37'),
+(23, 7, 'Clinical pharmacology and therapeutics|37'),
+(23, 8, 'Dermatology|37'),
+(23, 9, 'Endocrinology and diabetes|37'),
+(23, 10, 'Gastroenterology|37'),
+(23, 11, 'General internal medicine|37'),
+(23, 12, 'Genitourinary medicine|37'),
+(23, 13, 'Geriatric medicine|37'),
+(23, 14, 'Immunology|37'),
+(23, 15, 'Infectious diseases|37'),
+(23, 16, 'Medical oncology|37'),
+(23, 17, 'Medical ophthalmology|37'),
+(23, 18, 'Metabolic Medicine|37'),
+(23, 19, 'Neurologist|37'),
+(23, 20, 'Nuclear medicine|37'),
+(23, 21, 'Palliative medicine|37'),
+(23, 22, 'Pharmaceutical medicine|37'),
+(23, 23, 'Rehabilitation medicine|37'),
+(23, 24, 'Renal medicine|37'),
+(23, 25, 'Respiratory medicine|37'),
+(23, 26, 'Rheumatology|37'),
+(23, 27, 'Sport and exercise medicine|37'),
+(23, 28, 'Stroke medicine|37'),
+(23, 29, 'Tropical medicine|37')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 30, 'Chemical pathology|38'),
+(23, 31, 'Haematology (doctor)|38'),
+(23, 32, 'Histopathology (doctor)|38'),
+(23, 33, 'Medical microbiology and virology (doctor)|38')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 34, 'Child and adolescent psychiatry|39'),
+(23, 35, 'Forensic psychiatry|39'),
+(23, 36, 'General psychiatry|39'),
+(23, 37, 'Liaison psychiatry|39'),
+(23, 38, 'Medical psychotherapy|39'),
+(23, 39, 'Old age psychiatry|39'),
+(23, 40, 'Psychiatry of intellectual disability (PID)|39')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 41, 'Cardiothoracic surgeon|40'),
+(23, 42, 'General surgery|40'),
+(23, 43, 'Neurosurgeon|40'),
+(23, 44, 'Oral and maxillofacial surgery|40'),
+(23, 45, 'Otorhinolaryngology (ear, nose and throat (ENT) surgery)|40'),
+(23, 46, 'Paediatric surgery|40'),
+(23, 47, 'Plastic surgery|40'),
+(23, 48, 'Trauma and orthopaedic surgery|40'),
+(23, 49, 'Urology|40'),
+(23, 50, 'Vascular surgery|40')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 51, 'Paediatric cardiology|41'),
+(23, 52, 'Paediatrician|41')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 53, 'Communications and corporate affairs|69'),
+(23, 54, 'Performance and quality management|69'),
+(23, 55, 'Purchasing and contract management|69'),
+(23, 56, 'Strategic management|69')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 57, 'Administrative management|71'),
+(23, 58, 'Decontamination services management|71'),
+(23, 59, 'Facilities management|71'),
+(23, 60, 'Hotel services management|71'),
+(23, 61, 'Integrated urgent care/NHS 111 team leader|71')$$
+
+-- Add districts
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(24, 1, 'Chennai'),
+(24, 2, 'Bangalore'),
+(24, 3, 'Hyderabad'),
+(24, 4, 'Mumbai')$$
+
+-- Add SEO subsubcategories with proper parent-child relationships
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 1, 'Resume Writing Tips|5'),
+(25, 2, 'Cover Letter Examples|5'),
+(25, 3, 'CV Templates|5'),
+(25, 4, 'Personal Statement Guide|5'),
+(25, 5, 'Job Application Tips|5'),
+(25, 6, 'Interview Preparation|6'),
+(25, 7, 'Common Interview Questions|6'),
+(25, 8, 'Interview Techniques|6'),
+(25, 9, 'Interview Attire|6'),
+(25, 10, 'Interview Follow-up|6')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 11, 'Job Search Strategies|7'),
+(25, 12, 'Online Job Boards|7'),
+(25, 13, 'Networking Tips|7'),
+(25, 14, 'Social Media Job Search|7'),
+(25, 15, 'Job Fairs|7'),
+(25, 16, 'Career Planning|8'),
+(25, 17, 'Career Change Advice|8'),
+(25, 18, 'Professional Development|8'),
+(25, 19, 'Continuing Education|8'),
+(25, 20, 'Promotion Strategies|8')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 21, 'Healthcare Careers|9'),
+(25, 22, 'Medical Professions|9'),
+(25, 23, 'Nursing Careers|9'),
+(25, 24, 'Allied Health Professions|9'),
+(25, 25, 'Healthcare Administration|9'),
+(25, 26, 'Software Development|10'),
+(25, 27, 'Data Science|10'),
+(25, 28, 'Cybersecurity|10'),
+(25, 29, 'IT Support|10'),
+(25, 30, 'Web Development|10')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 31, 'Banking Careers|11'),
+(25, 32, 'Investment Banking|11'),
+(25, 33, 'Financial Analysis|11'),
+(25, 34, 'Accounting Careers|11'),
+(25, 35, 'Insurance Industry|11'),
+(25, 36, 'Teaching Careers|12'),
+(25, 37, 'Education Administration|12'),
+(25, 38, 'Higher Education|12'),
+(25, 39, 'Special Education|12'),
+(25, 40, 'Early Childhood Education|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 41, 'Recruitment Best Practices|13'),
+(25, 42, 'Talent Acquisition|13'),
+(25, 43, 'Interviewing Techniques|13'),
+(25, 44, 'Candidate Screening|13'),
+(25, 45, 'Job Description Writing|13'),
+(25, 46, 'Employee Retention|14'),
+(25, 47, 'Staff Development|14'),
+(25, 48, 'Employee Benefits|14'),
+(25, 49, 'Recognition Programs|14'),
+(25, 50, 'Reducing Turnover|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 51, 'Remote Work Policies|15'),
+(25, 52, 'Office Environment|15'),
+(25, 53, 'Team Building|15'),
+(25, 54, 'Workplace Culture|15'),
+(25, 55, 'Work-Life Balance|15'),
+(25, 56, 'Employment Law|16'),
+(25, 57, 'HR Compliance|16'),
+(25, 58, 'Workplace Safety|16'),
+(25, 59, 'Data Protection|16'),
+(25, 60, 'Equal Opportunity|16')$$
+
+-- Add job categories data to SEO categories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(21, 5, 'Allied health professionals'),
+(21, 6, 'Ambulance service team'),
+(21, 7, 'Dental team'),
+(21, 8, 'Doctors'),
+(21, 9, 'Estates and facilities'),
+(21, 10, 'Health informatics'),
+(21, 11, 'Healthcare science'),
+(21, 12, 'Healthcare support worker'),
+(21, 13, 'Management'),
+(21, 14, 'Medical associate professions'),
+(21, 15, 'Midwifery'),
+(21, 16, 'Nursing'),
+(21, 17, 'Pharmacy'),
+(21, 18, 'Psychological professions'),
+(21, 19, 'Public health'),
+(21, 20, 'Wider healthcare team')$$
+
+-- Add job subcategories data to SEO subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 17, 'Art therapist/art psychotherapist|5'),
+(22, 18, 'Diagnostic radiographer|5'),
+(22, 19, 'Dietitian|5'),
+(22, 20, 'Dramatherapist|5'),
+(22, 21, 'Music therapist|5'),
+(22, 22, 'Occupational therapist|5'),
+(22, 23, 'Operating department practitioner|5'),
+(22, 24, 'Orthoptist|5'),
+(22, 25, 'Osteopath|5'),
+(22, 26, 'Paramedic|5'),
+(22, 27, 'Physiotherapist|5'),
+(22, 28, 'Podiatrist|5'),
+(22, 29, 'Prosthetist/orthotist|5'),
+(22, 30, 'Speech and language therapist|5'),
+(22, 31, 'Therapeutic radiographer|5'),
+(22, 32, 'Ambulance care assistant and Patient Transport Service (PTS) driver|6'),
+(22, 33, 'Call handler/emergency medical dispatcher|6'),
+(22, 34, 'Emergency care assistant|6'),
+(22, 35, 'Emergency medical technician|6'),
+(22, 36, 'Experienced paramedic|6'),
+(22, 37, 'Patient transport service (PTS) call handler|6'),
+(22, 38, 'Dental|7'),
+(22, 39, 'Dental nurse|7'),
+(22, 40, 'Dental technician/ dental technologist|7'),
+(22, 41, 'Dental Therapist|7'),
+(22, 42, 'Dentist|7'),
+(22, 43, 'Anesthesia|8'),
+(22, 44, 'Clinical oncology|8'),
+(22, 45, 'Clinical radiology|8'),
+(22, 46, 'Community sexual and reproductive health|8'),
+(22, 47, 'Emergency medicine|8'),
+(22, 48, 'General practitioner|8'),
+(22, 49, 'Intensive care medicine|8'),
+(22, 50, 'Obstetrics and gynaecology|8'),
+(22, 51, 'Occupational medicine|8'),
+(22, 52, 'Ophthalmology|8'),
+(22, 53, 'Medicine Doctor|8'),
+(22, 54, 'Pathology Doctor|8'),
+(22, 55, 'Psychiatry Doctor|8'),
+(22, 56, 'Surgery Doctor|8'),
+(22, 57, 'Paediatrics|8')$$
+
+-- Add SEO subcategories for healthcare-related categories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+-- Dental team subcategories
+(22, 58, 'Dental News|7'),
+(22, 59, 'Dental Careers|7'),
+(22, 60, 'Dental Education|7'),
+(22, 61, 'Dental Technology|7'),
+
+-- Doctors subcategories
+(22, 62, 'Medical News|8'),
+(22, 63, 'Medical Research|8'),
+(22, 64, 'Clinical Practice|8'),
+(22, 65, 'Medical Education|8'),
+
+-- Nursing subcategories
+(22, 66, 'Nursing News|16'),
+(22, 67, 'Nursing Careers|16'),
+(22, 68, 'Nursing Education|16'),
+(22, 69, 'Clinical Skills|16')$$
+
+-- Add SEO subsubcategories with correct parent IDs referencing SEO subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+-- Medical topics under Medical News (subcategory_id 62)
+(25, 1, 'Acute internal medicine|62'),
+(25, 2, 'Allergy|62'),
+(25, 3, 'Audiovestibular medicine|62'),
+(25, 4, 'Cardiologist|62'),
+(25, 5, 'Clinical genetics|62'),
+(25, 6, 'Clinical neurophysiology|62'),
+(25, 7, 'Clinical pharmacology and therapeutics|62'),
+(25, 8, 'Dermatology|62'),
+(25, 9, 'Endocrinology and diabetes|62'),
+(25, 10, 'Gastroenterology|62'),
+(25, 11, 'General internal medicine|62'),
+(25, 12, 'Genitourinary medicine|62'),
+(25, 13, 'Geriatric medicine|62'),
+(25, 14, 'Immunology|62'),
+(25, 15, 'Infectious diseases|62'),
+
+-- Medical specialties under Clinical Practice (subcategory_id 64)
+(25, 16, 'Medical oncology|64'),
+(25, 17, 'Medical ophthalmology|64'),
+(25, 18, 'Metabolic Medicine|64'),
+(25, 19, 'Neurologist|64'),
+(25, 20, 'Nuclear medicine|64'),
+(25, 21, 'Palliative medicine|64'),
+(25, 22, 'Pharmaceutical medicine|64'),
+(25, 23, 'Rehabilitation medicine|64'),
+(25, 24, 'Renal medicine|64'),
+(25, 25, 'Respiratory medicine|64'),
+
+-- Surgical specialties under Dental News (subcategory_id 58)
+(25, 26, 'Dental Surgery|58'),
+(25, 27, 'Orthodontics|58'),
+(25, 28, 'Periodontics|58'),
+(25, 29, 'Endodontics|58'),
+(25, 30, 'Prosthodontics|58'),
+(25, 31, 'Pediatric Dentistry|58'),
+(25, 32, 'Oral Medicine|58'),
+(25, 33, 'Dental Public Health|58'),
+
+-- Psychiatric specialties under Medical Research (subcategory_id 63)
+(25, 34, 'Child and adolescent psychiatry|63'),
+(25, 35, 'Forensic psychiatry|63'),
+(25, 36, 'General psychiatry|63'),
+(25, 37, 'Liaison psychiatry|63'),
+(25, 38, 'Medical psychotherapy|63'),
+(25, 39, 'Old age psychiatry|63'),
+(25, 40, 'Psychiatry of intellectual disability|63'),
+
+-- Surgical specialties under Medical Education (subcategory_id 65)
+(25, 41, 'Cardiothoracic surgery|65'),
+(25, 42, 'General surgery|65'),
+(25, 43, 'Neurosurgery|65'),
+(25, 44, 'Oral and maxillofacial surgery|65'),
+(25, 45, 'Otorhinolaryngology|65'),
+(25, 46, 'Paediatric surgery|65'),
+(25, 47, 'Plastic surgery|65'),
+(25, 48, 'Trauma and orthopaedic surgery|65'),
+(25, 49, 'Urology|65'),
+(25, 50, 'Vascular surgery|65')$$
+
+-- Add master data for permission types
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(26, 0, 'VIEW_APPLICANTS'),
+(26, 1, 'CONTACT_APPLICANTS'),
+(26, 2, 'POST_JOBS'),
+(26, 3, 'FEATURED_JOBS'),
+(26, 4, 'ANALYTICS'),
+(26, 5, 'BULK_ACTIONS')$$
+
+-- Add master data for subscription plan permissions
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(27, 0, '000000'), -- Free Plan - No permissions
+(27, 1, '000000'), -- Basic Plan - Not used, no permissions
+(27, 2, '101110'), -- Standard Plan ("starter") - VIEW_APPLICANTS, POST_JOBS, FEATURED_JOBS, BULK_ACTIONS
+(27, 3, '111110'), -- Premium Plan ("advance") - VIEW_APPLICANTS, CONTACT_APPLICANTS, POST_JOBS, FEATURED_JOBS, ANALYTICS
+(27, 4, '111111'), -- Enterprise Plan - All permissions
+(27, 5, '101110')  -- Trial Plan - Same as Standard Plan
 $$
+
+-- Add subscription plan data with permissions (without plan_object as it will store Stripe price IDs)
+-- Plan IDs match the constants in ConstantsUtil.java:
+-- SUBSCRIPTION_STANDARD_PLAN = 2
+-- SUBSCRIPTION_PREMIUM_PLAN = 3
+-- SUBSCRIPTION_ENTERPRISE_PLAN = 4
+-- SUBSCRIPTION_TRIAL_PLAN = 5
+INSERT INTO subscription_plan (plan_id, plan_name, permissions) VALUES
+(5, 'Trial', '101110'),
+(2, 'Standard Monthly', '101110'),
+(20, 'Standard Yearly', '101110'),
+(3, 'Premium Monthly', '111110'),
+(30, 'Premium Yearly', '111110'),
+(4, 'Enterprise Monthly', '111111'),
+(40, 'Enterprise Yearly', '111111')
+$$
+
+-- Add subcategories for Estates and facilities (category_id 5)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 123, 'Catering manager|5'),
+(17, 124, 'Chef/cook|5'),
+(17, 125, 'Domestic services staff|5'),
+(17, 126, 'Housekeeper|5'),
+(17, 127, 'Linen services staff|5'),
+(17, 128, 'Bricklayer|5'),
+(17, 129, 'Caretaker|5'),
+(17, 130, 'Carpenter/joiner|5'),
+(17, 131, 'Electrician|5'),
+(17, 132, 'Engineer|5'),
+(17, 133, 'Estates technician|5'),
+(17, 134, 'Gardeners and grounds staff|5'),
+(17, 135, 'Painter and decorator|5'),
+(17, 136, 'Plumber|5'),
+(17, 137, 'Surveyor|5'),
+(17, 138, 'Tiler|5'),
+(17, 139, 'Window cleaner|5'),
+(17, 140, 'Driver|5'),
+(17, 141, 'Fire safety officer|5'),
+(17, 142, 'Health and safety officer|5'),
+(17, 143, 'Porter|5'),
+(17, 144, 'Security staff|5'),
+(17, 145, 'Stores and distribution staff|5')$$
+
+-- Add subcategories for Health informatics (category_id 6)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 146, 'Clinical bioinformatics|6')$$
+
+-- Add subcategories for Healthcare science (category_id 7)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 147, 'Clinical bioinformatics (genomics)|7'),
+(17, 148, 'Clinical bioinformatics (health informatics)|7'),
+(17, 149, 'Clinical bioinformatics (physical sciences)|7')$$
+
+-- Add subcategories for Wider healthcare team (category_id 16)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 150, 'Clerk|16'),
+(17, 151, 'Health records staff|16'),
+(17, 152, 'Medical secretary/personal assistant|16'),
+(17, 153, 'Receptionist|16'),
+(17, 154, 'Secretary/typist|16'),
+(17, 155, 'Telephonist/switchboard operator|16'),
+(17, 156, 'Assistant practitioner|16'),
+(17, 157, 'Cardiographer|16'),
+(17, 158, 'Creative therapy support roles|16'),
+(17, 159, 'Dental support worker|16'),
+(17, 160, 'Donor carer|16'),
+(17, 161, 'Employment specialist|16'),
+(17, 162, 'Health play staff|16'),
+(17, 163, 'Healthcare science assistants and associates|16'),
+(17, 164, 'Integrated urgent care/NHS 111 roles|16'),
+(17, 165, 'Mammographer|16'),
+(17, 166, 'Medical support worker|16'),
+(17, 167, 'Newborn hearing screener|16'),
+(17, 168, 'Nutritionist|16'),
+(17, 169, 'Optometrist|16'),
+(17, 170, 'Orthopaedic practitioner|16'),
+(17, 171, 'Phlebotomist|16'),
+(17, 172, 'Social prescribing link worker|16'),
+(17, 173, 'Social worker|16'),
+(17, 174, 'Support, time and recovery worker|16'),
+(17, 175, 'Theatre support worker|16'),
+(17, 176, 'Arts manager/arts co-ordinator|16'),
+(17, 177, 'Chaplain|16'),
+(17, 178, 'Communications/public relations staff|16'),
+(17, 179, 'Finance staff|16'),
+(17, 180, 'Human resources staff|16'),
+(17, 181, 'Nursery nurse and nursery assistant|16')$$
+
+-- Add sub-subcategories for Domestic services (subcategory_id 42)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 62, 'Catering manager|42'),
+(23, 63, 'Chef/cook|42'),
+(23, 64, 'Domestic services staff|42'),
+(23, 65, 'Housekeeper|42'),
+(23, 66, 'Linen services staff|42')$$
+
+-- Add sub-subcategories for Estates services (subcategory_id 43)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 67, 'Bricklayer|43'),
+(23, 68, 'Caretaker|43'),
+(23, 69, 'Carpenter/joiner|43'),
+(23, 70, 'Electrician|43'),
+(23, 71, 'Engineer|43'),
+(23, 72, 'Estates technician|43'),
+(23, 73, 'Gardeners and grounds staff|43'),
+(23, 74, 'Painter and decorator|43'),
+(23, 75, 'Plumber|43'),
+(23, 76, 'Surveyor|43'),
+(23, 77, 'Tiler|43'),
+(23, 78, 'Window cleaner|43')$$
+
+-- Add sub-subcategories for Support services (subcategory_id 44)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 79, 'Driver|44'),
+(23, 80, 'Fire safety officer|44'),
+(23, 81, 'Health and safety officer|44'),
+(23, 82, 'Porter|44'),
+(23, 83, 'Security staff|44'),
+(23, 84, 'Stores and distribution staff|44')$$
+
+-- Add sub-subcategories for Clinical bioinformatics (subcategory_id 52)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 85, 'Clinical bioinformatics (genomics)|52'),
+(23, 86, 'Clinical bioinformatics (health informatics)|52'),
+(23, 87, 'Clinical bioinformatics (physical sciences)|52')$$
+
+-- Add sub-subcategories for Life sciences (subcategory_id 53)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 88, 'Analytical toxicology|53'),
+(23, 89, 'Anatomical pathology|53'),
+(23, 90, 'Biomedical science|53'),
+(23, 91, 'Cancer genomics|53'),
+(23, 92, 'Clinical biochemistry|53'),
+(23, 93, 'Clinical immunology|53'),
+(23, 94, 'Cytopathology|53'),
+(23, 95, 'Genomic counselling|53'),
+(23, 96, 'Genomics|53'),
+(23, 97, 'Haematology (healthcare scientist)|53'),
+(23, 98, 'Infection sciences|53'),
+(23, 99, 'Microbiology (healthcare scientist)|53'),
+(23, 100, 'Reproductive science and andrology|53'),
+(23, 101, 'Virology (healthcare scientist)|53')$$
+
+-- Add sub-subcategories for Physical sciences and biomedical engineering (subcategory_id 54)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 102, 'Clinical measurement|54'),
+(23, 103, 'Clinical or medical technology in medical physics|54'),
+(23, 104, 'Clinical pharmaceutical science|54'),
+(23, 105, 'Clinical photography|54'),
+(23, 106, 'Decontamination science (sterile services and flexible endoscopy)|54'),
+(23, 107, 'Imaging (ionising)|54'),
+(23, 108, 'Imaging (non-ionising)|54'),
+(23, 109, 'Medical device risk management and governance|54'),
+(23, 110, 'Medical engineering|54'),
+(23, 111, 'Nuclear medicine (healthcare scientist)|54'),
+(23, 112, 'Radiation physics and radiation safety physics|54'),
+(23, 113, 'Radiotherapy physics|54'),
+(23, 114, 'Reconstructive science|54'),
+(23, 115, 'Rehabilitation engineering|54'),
+(23, 116, 'Renal technology|54')$$
+
+-- Add sub-subcategories for Physiological sciences (subcategory_id 55)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 117, 'Audiology|55'),
+(23, 118, 'Cardiac sciences|55'),
+(23, 119, 'Clinical exercise physiologist|55'),
+(23, 120, 'Clinical perfusion science|55'),
+(23, 121, 'Critical care science|55'),
+(23, 122, 'Gastrointestinal physiology|55'),
+(23, 123, 'Hearing aid dispenser|55'),
+(23, 124, 'Neurophysiology|55'),
+(23, 125, 'Ophthalmic and vision science|55'),
+(23, 126, 'Respiratory physiology and sleep sciences|55'),
+(23, 127, 'Urodynamic science|55'),
+(23, 128, 'Vascular science|55')$$
+
+-- Add sub-subcategories for Wider healthcare team (subcategory_id 120)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 129, 'Clerk|120'),
+(23, 130, 'Health records staff|120'),
+(23, 131, 'Medical secretary/personal assistant|120'),
+(23, 132, 'Receptionist|120'),
+(23, 133, 'Secretary/typist|120'),
+(23, 134, 'Telephonist/switchboard operator|120')$$
+
+-- Add sub-subcategories for Clinical support staff (subcategory_id 121)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 135, 'Assistant practitioner|121'),
+(23, 136, 'Cardiographer|121'),
+(23, 137, 'Creative therapy support roles|121'),
+(23, 138, 'Dental support worker|121'),
+(23, 139, 'Donor carer|121'),
+(23, 140, 'Employment specialist|121'),
+(23, 141, 'Health play staff|121'),
+(23, 142, 'Healthcare science assistants and associates|121'),
+(23, 143, 'Integrated urgent care/NHS 111 roles|121'),
+(23, 144, 'Mammographer|121'),
+(23, 145, 'Medical support worker|121'),
+(23, 146, 'Newborn hearing screener|121'),
+(23, 147, 'Nutritionist|121'),
+(23, 148, 'Optometrist|121'),
+(23, 149, 'Orthopaedic practitioner|121'),
+(23, 150, 'Phlebotomist|121'),
+(23, 151, 'Social prescribing link worker|121'),
+(23, 152, 'Social worker|121'),
+(23, 153, 'Support, time and recovery worker|121'),
+(23, 154, 'Theatre support worker|121')$$
+
+-- Add sub-subcategories for Corporate services (subcategory_id 122)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 155, 'Arts manager/arts co-ordinator|122'),
+(23, 156, 'Chaplain|122'),
+(23, 157, 'Communications/public relations staff|122'),
+(23, 158, 'Finance staff|122'),
+(23, 159, 'Human resources staff|122'),
+(23, 160, 'Nursery nurse and nursery assistant|122')$$
+
Index: target/classes/application-dev.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#spring.jpa.hibernate.ddl-auto=update\r\n#spring.jpa.hibernate.ddl-auto=create-drop\r\nspring.jpa.hibernate.ddl-auto=create\r\n#spring.jpa.hibernate.ddl-auto=none\r\nspring.datasource.url=*************************************\r\nspring.datasource.username=root\r\nspring.datasource.password=system123#\r\nspring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver\r\npring.jpa.database-platform=org.hibernate.dialect.MySQLDialect\r\nspring.jpa.show-sql=true\r\njwt.secret=jobportalapp\r\nspring.jpa.properties.hibernate.jdbc.time_zone=UTC\r\napplication.baseUrl=http://localhost:8080\r\napplication.baseFrontendUrl=http://app.localhost:3000\r\napplication.email=<EMAIL>\r\napplication.ticketEmail=<EMAIL>\r\napplication.certificatecode=JB\r\napplication.name=Job Portal\r\napplication.description=Portal for Applying Jobs under several department\r\napplication.version=1.0.0\r\nserver.port=8080\r\nspring.sql.init.mode=embedded\r\n#spring.sql.init.mode=never\r\napp.database.initialize=true\r\nspring.jpa.defer-datasource-initialization=true\r\n#spring.flyway.enabled=true\r\n#spring.jpa.defer-datasource-initialization=false\r\nspring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER\r\nspring.servlet.multipart.max-file-size=-1\r\nspring.servlet.multipart.max-request-size=-1\r\napplication.aws.bucketname=ebrainyvideostreamingtestss\r\napplication.aws.accessKey=********************\r\napplication.aws.secretKey=u6Fy39Cx1BMdVAwXxfWr9j2YKmcGDksgLsDqJXQd\r\napplication.aws.region=eu-north-1\r\napplication.aws.secretName=stripe\r\n#mail\r\nspring.mail.host=smtp.gmail.com\r\nspring.mail.port=587\r\nspring.mail.username=<EMAIL>\r\nspring.mail.password=aoamtqqqspnwjuvu\r\nspring.mail.properties.mail.smtp.auth=true\r\nspring.mail.properties.mail.smtp.starttls.enable=true\r\n#sms\r\nsms.PHONE_NUMBER=+16203901757\r\n#payment\r\n# Razorpay\r\nstripe.webhook.signing.currency=GBP\r\napplication.multiCurrency=false\r\napplication.multiCurrencyList=USD,GBP,EUR\r\n#security\r\nspring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com\r\nspring.security.oauth2.client.registration.google.clientSecret=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\r\nspring.security.oauth2.client.registration.google.scope=email, profile\r\nspring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.registration.facebook.clientId=1456102035268496\r\nspring.security.oauth2.client.registration.facebook.clientSecret=********************************\r\nspring.security.oauth2.client.registration.facebook.scope=email, public_profile\r\nspring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth\r\nspring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token\r\n#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified\r\napp.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1\r\napp.auth.tokenExpirationMsec=864000000\r\napp.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*\r\napp.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect\r\n#logs\r\nlogging.level.root=INFO\r\nlogging.config=classpath:logback-spring.xml\r\nlogging.level.org.springframework=INFO\r\nlogging.level.org.springframework.boot=INFO\r\nlogging.level.org.springframework.boot.autoconfigure=INFO\r\nlogging.level.org.springframework.boot.context=INFO\r\nlogging.level.org.springframework.boot.devtools=INFO\r\nlogging.level.org.springframework.web=INFO\r\nspring.devtools.restart.enabled=false\r\n\r\n\r\n\r\n\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/target/classes/application-dev.properties b/target/classes/application-dev.properties
--- a/target/classes/application-dev.properties	(revision 9676afa95e256f112046ca3480538c3164945ee5)
+++ b/target/classes/application-dev.properties	(date 1748428157339)
@@ -4,42 +4,84 @@
 #spring.jpa.hibernate.ddl-auto=none
 spring.datasource.url=*************************************
 spring.datasource.username=root
-spring.datasource.password=system123#
+spring.datasource.password=root
 spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
-pring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
+#spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
 spring.jpa.show-sql=true
 jwt.secret=jobportalapp
 spring.jpa.properties.hibernate.jdbc.time_zone=UTC
 application.baseUrl=http://localhost:8080
-application.baseFrontendUrl=http://app.localhost:3000
-application.email=<EMAIL>
+# application.baseFrontendUrl=http://app.localhost:3000
+application.baseFrontendUrl=http://localhost:3000
+management.endpoints.web.exposure.include=health,info,metrics
+# Optionally, configure access to the health endpoint
+management.endpoint.health.probe.enabled=true
+management.endpoints.web.base-path=/actuator
+management.endpoint.health.show-details=always
 application.ticketEmail=<EMAIL>
 application.certificatecode=JB
 application.name=Job Portal
 application.description=Portal for Applying Jobs under several department
 application.version=1.0.0
+app.base-url=https://www.groglojobs.co.uk
 server.port=8080
 spring.sql.init.mode=embedded
 #spring.sql.init.mode=never
 app.database.initialize=true
+spring.jpa.open-in-view=false
 spring.jpa.defer-datasource-initialization=true
 #spring.flyway.enabled=true
 #spring.jpa.defer-datasource-initialization=false
 spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
 spring.servlet.multipart.max-file-size=-1
 spring.servlet.multipart.max-request-size=-1
-application.aws.bucketname=ebrainyvideostreamingtestss
-application.aws.accessKey=********************
-application.aws.secretKey=u6Fy39Cx1BMdVAwXxfWr9j2YKmcGDksgLsDqJXQd
+application.aws.bucketname=ebrainyvideostreaming
+application.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com
+application.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net
+application.aws.accessKey=********************
+application.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM
 application.aws.region=eu-north-1
 application.aws.secretName=stripe
+
+application.email=<EMAIL>
+#email.domain.from=<EMAIL>
+
+# Social media links
+social.facebook.url=https://www.facebook.com/groglojobs
+social.linkedin.url=https://www.linkedin.com/company/groglojobs
+social.twitter.url=https://twitter.com/groglojobs
+
+# Social media icons
+social.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png
+social.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png
+social.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png
+
+
 #mail
+email.provider=domain
+
+# gmail
 spring.mail.host=smtp.gmail.com
 spring.mail.port=587
 spring.mail.username=<EMAIL>
 spring.mail.password=aoamtqqqspnwjuvu
+#domain
+#email.domain.host=smtp.zoho.in
+#email.domain.port=465
+#email.domain.username=<EMAIL>
+#email.domain.password=rfxVXje1NXUz
+#email.domain.from=<EMAIL>
+email.domain.host=smtp.zoho.eu
+email.domain.port=465
+email.domain.username=<EMAIL>
+email.domain.password=vNsnxLakWUnr
+email.domain.from=<EMAIL>
+
+
+
 spring.mail.properties.mail.smtp.auth=true
 spring.mail.properties.mail.smtp.starttls.enable=true
+
 #sms
 sms.PHONE_NUMBER=+16203901757
 #payment
@@ -74,8 +116,15 @@
 logging.level.org.springframework.web=INFO
 spring.devtools.restart.enabled=false
 
-
-
+# stripe
+stripe.api.key=sk_test_51R6oOD4YEQBprOqE5njXOiLJbv3yOi3XZUVKOgMUpeSFBxG1BB6h9DMUR3QRJYkCKisdksd1UmAxEp5Y5OVNKTG1002yeP6bV1
+stripe.webhook.signing.secret=whsec_3e18ca3d7014104d8b29a889e2ead2513989296abded91963fd6ae676d931ba6
+stripe.price.standard.monthly=price_1R78mJ4YEQBprOqE5tTXs86x
+stripe.price.standard.yearly=price_standard_yearly_id
+stripe.price.premium.monthly=price_1R78mw4YEQBprOqEDP69lLFW
+stripe.price.premium.yearly=price_premium_yearly_id
+stripe.price.enterprise.monthly=price_enterprise_monthly_id
+stripe.price.enterprise.yearly=price_enterprise_yearly_id
 
 
 
