package com.job.jobportal.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class MarketingUserDTO {

    private String userName;

    private String mobileNo;

    private String emailId;

    private Timestamp createdOn;

    private int isActive;

    // Added field to distinguish between users and subscribers
    private String userType; // "USER" or "SUBSCRIBER"

    // Constructor for regular users
    public MarketingUserDTO() {
        this.userType = "USER";
    }

    // Constructor for subscribers
    public MarketingUserDTO(String emailId, Timestamp createdOn, String userType) {
        this.emailId = emailId;
        this.createdOn = createdOn;
        this.userType = userType;
        this.isActive = 1; // Subscribers are considered active by default
    }
}
