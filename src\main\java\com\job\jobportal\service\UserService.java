package com.job.jobportal.service;

import com.job.jobportal.dto.ChangeUserDetailsDTO;
import com.job.jobportal.dto.MarketingUserDTO;
import com.job.jobportal.dto.UserDTO;
import com.job.jobportal.dto.UserListDTO;
import com.job.jobportal.model.AccountDetails;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Roles;
import com.job.jobportal.model.Subscriber;
import com.job.jobportal.repository.AccountDetailsRepo;
import com.job.jobportal.repository.PasswordResetTokenRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.RolesRepository;
import com.job.jobportal.security.AuthProvider;
import com.job.jobportal.security.PasswordResetToken;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.security.oauth2.user.OAuth2UserInfo;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

//import com.gamedev.security.UserPrinciple;


@Service
public class UserService {


    @Autowired
    RegisteruserRepository userRepo;

    @Autowired
    AccountDetailsRepo accountDetailsRepo;


    @Autowired
    PasswordResetTokenRepo passwordResetTokenRepo;
    @Autowired
    EmailService emailService;
    @Autowired
    private RolesRepository rolesRepo;
    @Autowired
    private SubscriberService subscriberService;
    @Value("${application.email}")
    private String email;

    @Value("${application.name}")
    private String name;

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);


    public Registereduser repoSaveUser(Registereduser user) {
        Registereduser userSaved = userRepo.save(user);
        String body = "The following user is registered for Amildham :\n" + "\n" +
                "First Name -" + user.getFirstname() + "\n" + "\n" +
                "Last Name -" + user.getLastname() + "\n" + "\n" +
                "Email -" + user.getEmail() + "\n" + "\n" +
                "Mobile Number -" + user.getMobileno() + "\n" + "\n";

        emailService.sendEmail(email, email, "New User Registered in " + name, body);
        return userSaved;
    }


    public Registereduser saveUser(UserDTO user) throws Exception {
        try {

            if (userRepo.findByEmail(user.getEmailId()).isPresent()) {
                throw new Exception("There is an account with that email address: " + user.getEmailId());
            } else {
                Registereduser regUser = new Registereduser();
                Calendar cal = Calendar.getInstance();
                regUser.setCreatedOn(new Timestamp(cal.getTimeInMillis()));
                regUser.setEmail(user.getEmailId());
                regUser.setFirstname(user.getFirstName());
                regUser.setLastname(user.getLastName());
                regUser.setMobileno(user.getMobileNo());
                //regUser.setPassword(bcryptEncoder.encode(user.getPassword()));
                regUser.setPassword(user.getPassword());
                regUser.setUsername(user.getEmailId());
                Set<Roles> roleSet = new HashSet<>();
                roleSet.add(rolesRepo.findByRolename(user.getRoleName()));
                regUser.setRoles(roleSet);


                regUser.setRefreshToken(user.getRefreshToken());
                regUser.setProvider(user.getAuthType());
                regUser.setHasPassword(user.getHasPassword());
                AccountDetails accountDetails = new AccountDetails();
                accountDetails.setIsPremiumAccount(ConstantsUtil.PREMIUM_ACCOUNT_ACTIVE_INACTIVE);
                accountDetails.setIsDeleteScheduled(ConstantsUtil.DELETE_ACCOUNT_ACTIVE_INACTIVE);
                accountDetails.setStorageUse(0L);
                accountDetails.setIsActive(ConstantsUtil.USER_ACTIVE);
                regUser.setAccountDetails(accountDetails);
                Registereduser userSaved = repoSaveUser(regUser);

                return userSaved;
            }
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public int updatePassword(String encodedPassword, Long userId) throws Exception {

        return userRepo.updatePassword(encodedPassword, userId);

    }

    public int updatePasswordWithAuthType(String encodedPassword, Long userId, AuthProvider authProvider) throws Exception {

        return userRepo.updatePasswordwithAuthType(encodedPassword, 1, userId);

    }

    public int updateUserDetails(ChangeUserDetailsDTO changeUserDetailsDTO, Long userId) throws Exception {

        return userRepo.updateUserDetails(changeUserDetailsDTO.getFirstName(), changeUserDetailsDTO.getLastName(), changeUserDetailsDTO.getMobileNo(), userId);

    }

    public Map<String, Object> getUser() {
        try {
            UserPrincipal princple = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            Optional<Registereduser> user = userRepo.findById(princple.getId());
            Map<String, Object> map = new HashMap<>();


            if (user.isPresent()) {
                Registereduser temp = user.get();
                map.put("userid", temp.getUserid());
                map.put("username", temp.getUsername());
                map.put("firstname", temp.getFirstname());
                map.put("lastname", temp.getLastname());
                map.put("mobileno", temp.getMobileno());
                map.put("email", temp.getEmail());
                map.put("createdOn", temp.getCreatedOn());
                map.put("roles", temp.getRoles());


            }

            return map;
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }


    public Optional<Registereduser> findByEmail(String email) {
        try {
            return userRepo.findByEmail(email);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public Optional<Registereduser> findById(Long userId) {
        try {
            return userRepo.findById(userId);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public List<Registereduser> getAllUser() {
        try {

            return userRepo.findAll();

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }


    public Registereduser registerNewUser(OAuth2UserRequest oAuth2UserRequest, OAuth2UserInfo oAuth2UserInfo) {
        try {
            Registereduser user = new Registereduser();

            user.setProvider(AuthProvider.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId()));
            user.setProviderId(oAuth2UserInfo.getId());
            user.setUsername(oAuth2UserInfo.getName());
            user.setEmail(oAuth2UserInfo.getEmail());
            user.setFirstname(oAuth2UserInfo.getName());
            Calendar cal = Calendar.getInstance();
            user.setCreatedOn(new Timestamp(cal.getTimeInMillis()));
            user.setImageUrl(oAuth2UserInfo.getImageUrl());
            Set<Roles> roleSet = new HashSet<>();
            roleSet.add(rolesRepo.findByRolename(ConstantsUtil.USER_ROLE));
            user.setRoles(roleSet);
            AccountDetails accountDetails = new AccountDetails();
            accountDetails.setIsPremiumAccount(ConstantsUtil.PREMIUM_ACCOUNT_ACTIVE_INACTIVE);
            accountDetails.setIsDeleteScheduled(ConstantsUtil.DELETE_ACCOUNT_ACTIVE_INACTIVE);
            accountDetails.setStorageUse(0L);
            accountDetails.setIsActive(ConstantsUtil.USER_ACTIVE);
            user.setAccountDetails(accountDetails);
            return repoSaveUser(user);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public Registereduser updateExistingUser(Registereduser existingUser, OAuth2UserInfo oAuth2UserInfo) {
        try {
            existingUser.setUsername(oAuth2UserInfo.getName());
            existingUser.setImageUrl(oAuth2UserInfo.getImageUrl());
            return userRepo.save(existingUser);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void updateAuthenticationType(String username, String oauth2ClientName) {
        try {
            AuthProvider authType = AuthProvider.valueOf(oauth2ClientName.toUpperCase());
            userRepo.updateAuthenticationType(authType, username);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void updateNotificationToken(String notificationToken) {
        try {
            UserPrincipal princple = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            userRepo.updateNotificationToken(notificationToken, princple.getId());

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void updateRefreshToken(String refreshToken, Long userId) {
        try {
           // String token = UUID.randomUUID().toString();
            PasswordResetToken myToken = passwordResetTokenRepo.findByToken(refreshToken).get();
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MONTH, 6);
            myToken.setToken(refreshToken);
            Date date = new Date(cal.getTimeInMillis());
            myToken.setExpiryDate(date);
            passwordResetTokenRepo.save(myToken);
            userRepo.updateRefreshToken(refreshToken, userId);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }


    public void createPasswordResetTokenForUser(Long userId, String token, boolean refreshToken) {
        PasswordResetToken myToken = new PasswordResetToken();
        Calendar cal = Calendar.getInstance();
        if (refreshToken) {
            cal.add(Calendar.MONTH, 6);
        } else {
            cal.add(Calendar.DATE, 2);
        }

        myToken.setToken(token);
        myToken.setUserId(userId);
        Date date = new Date(cal.getTimeInMillis());
        myToken.setExpiryDate(date);
        passwordResetTokenRepo.save(myToken);
    }

    public String validatePasswordResetToken(String token) {

        Optional<PasswordResetToken> passToken = findByToken(token);
        return passToken.isEmpty() ? "invalidToken"
                : isTokenExpired(passToken.get()) ? "expired"
                : null;
    }

    public Optional<PasswordResetToken> findByToken(String token) {
        return passwordResetTokenRepo.findByToken(token);
    }


    private boolean isTokenExpired(PasswordResetToken passToken) {
        final Calendar cal = Calendar.getInstance();
        return passToken.getExpiryDate().before(cal.getTime());
    }

    public Page<MarketingUserDTO> getAllUser(Integer hasCourse, String userType, Pageable pageable) {
        try {
            List<MarketingUserDTO> allUsers = new ArrayList<>();

            // Add regular users if userType is null, "ALL", or "USER"
            if (userType == null || userType.equalsIgnoreCase("ALL") || userType.equalsIgnoreCase("USER")) {
                // Use repository pagination for users when pageable is provided
                if (pageable != null) {
                    Page<Registereduser> userPage = userRepo.findAllByMarketingUserPaginated(ConstantsUtil.USER_ROLE, pageable);
                    for (Registereduser user : userPage.getContent()) {
                        MarketingUserDTO marketingUserDTO = new MarketingUserDTO();
                        String userName = user.getFirstname();
                        if (user.getLastname() != null) {
                            userName = userName + " " + user.getLastname();
                        }
                        marketingUserDTO.setUserName(userName);
                        marketingUserDTO.setMobileNo(user.getMobileno());
                        marketingUserDTO.setCreatedOn(user.getCreatedOn());
                        marketingUserDTO.setEmailId(user.getEmail());
                        marketingUserDTO.setIsActive(user.getAccountDetails().getIsActive());
                        marketingUserDTO.setUserType("USER");

//            if(hasCourse!=null) {
//                if (hasCourse == 1) {
//                    marketingUserDTO.setCourseList(purchaseCourseService.getCourseByPurchasedCourse(user.getUserid()));
//                }
//            }
                        allUsers.add(marketingUserDTO);
                    }
                } else {
                    // Fallback to original method for backward compatibility
                    List<Registereduser> r = userRepo.findAllByMarketingUser(ConstantsUtil.USER_ROLE);
                    for (Registereduser user : r) {
                        MarketingUserDTO marketingUserDTO = new MarketingUserDTO();
                        String userName = user.getFirstname();
                        if (user.getLastname() != null) {
                            userName = userName + " " + user.getLastname();
                        }
                        marketingUserDTO.setUserName(userName);
                        marketingUserDTO.setMobileNo(user.getMobileno());
                        marketingUserDTO.setCreatedOn(user.getCreatedOn());
                        marketingUserDTO.setEmailId(user.getEmail());
                        marketingUserDTO.setIsActive(user.getAccountDetails().getIsActive());
                        marketingUserDTO.setUserType("USER");

//            if(hasCourse!=null) {
//                if (hasCourse == 1) {
//                    marketingUserDTO.setCourseList(purchaseCourseService.getCourseByPurchasedCourse(user.getUserid()));
//                }
//            }
                        allUsers.add(marketingUserDTO);
                    }
                }
            }

            // Add subscribers if userType is null, "ALL", or "SUBSCRIBER"
            if (userType == null || userType.equalsIgnoreCase("ALL") || userType.equalsIgnoreCase("SUBSCRIBER")) {
                if (pageable != null) {
                    // Use repository pagination for subscribers
                    Page<Subscriber> subscriberPage = subscriberService.getActiveSubscribersPaginated(pageable);
                    for (Subscriber subscriber : subscriberPage.getContent()) {
                        MarketingUserDTO marketingUserDTO = new MarketingUserDTO(
                            subscriber.getEmail(),
                            subscriber.getCreatedAt(),
                            "SUBSCRIBER"
                        );
                        marketingUserDTO.setUserName("Subscriber"); // Default name for subscribers
                        allUsers.add(marketingUserDTO);
                    }
                } else {
                    // Fallback to original method for backward compatibility
                    List<Subscriber> subscribers = subscriberService.getActiveSubscribers();
                    for (Subscriber subscriber : subscribers) {
                        MarketingUserDTO marketingUserDTO = new MarketingUserDTO(
                            subscriber.getEmail(),
                            subscriber.getCreatedAt(),
                            "SUBSCRIBER"
                        );
                        marketingUserDTO.setUserName("Subscriber"); // Default name for subscribers
                        allUsers.add(marketingUserDTO);
                    }
                }
            }

            // If pageable is null, return all data as a single page for backward compatibility
            if (pageable == null) {
                // Sort by creation date (newest first) when not using repository pagination
                allUsers.sort((a, b) -> b.getCreatedOn().compareTo(a.getCreatedOn()));
                return new PageImpl<>(allUsers);
            }

            // When using mixed data sources (users + subscribers), we need to handle pagination manually
            if ((userType == null || userType.equalsIgnoreCase("ALL")) &&
                allUsers.size() > pageable.getPageSize()) {
                // Sort by creation date (newest first)
                allUsers.sort((a, b) -> b.getCreatedOn().compareTo(a.getCreatedOn()));

                // Apply pagination manually for mixed data
                int start = (int) pageable.getOffset();
                int end = Math.min((start + pageable.getPageSize()), allUsers.size());

                List<MarketingUserDTO> pageContent = start < end ? allUsers.subList(start, end) : new ArrayList<>();
                return new PageImpl<>(pageContent, pageable, allUsers.size());
            }

            // For single data source, return as-is (already paginated by repository)
            return new PageImpl<>(allUsers, pageable != null ? pageable : Pageable.unpaged(), allUsers.size());

        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    // Overloaded method for backward compatibility
    public List<MarketingUserDTO> getAllUser(Integer hasCourse) {
        Page<MarketingUserDTO> page = getAllUser(hasCourse, null, null);
        return page.getContent();
    }

    public Page<UserListDTO> getFilteredUsers(String role, String email, String sortBy, String sortDirection, int page, int size) {
        try {
            List<Registereduser> users = userRepo.findByRoleAndEmailContainingFetchAll(role, email);

            List<UserListDTO> dtos = users.stream()
                .map(UserListDTO::fromEntity)
                .collect(Collectors.toList());

            Comparator<UserListDTO> comparator = getUserListDTOComparator(sortBy, sortDirection);

            dtos.sort(comparator);

            int fromIndex = page * size;
            int toIndex = Math.min(fromIndex + size, dtos.size());

            List<UserListDTO> pagedDtos = fromIndex < toIndex
                ? dtos.subList(fromIndex, toIndex)
                : new ArrayList<>();

            return new PageImpl<>(pagedDtos, PageRequest.of(page, size), dtos.size());
        } catch (Exception e) {
            logger.error("Error filtering users: " + e.getMessage());
            throw e;
        }
    }

    private static Comparator<UserListDTO> getUserListDTOComparator(String sortBy, String sortDirection) {
        Comparator<UserListDTO> comparator;
        if (sortBy.equalsIgnoreCase("firstName") || sortBy.equalsIgnoreCase("firstname")) {
            comparator = Comparator.comparing(UserListDTO::getFirstname,
                Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER));
        } else if (sortBy.equalsIgnoreCase("lastName") || sortBy.equalsIgnoreCase("lastname")) {
            comparator = Comparator.comparing(UserListDTO::getLastname,
                Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER));
        } else {
            comparator = Comparator.comparing(UserListDTO::getUserid);
        }

        if (sortDirection.equalsIgnoreCase("desc")) {
            comparator = comparator.reversed();
        }
        return comparator;
    }

    public List<UserListDTO> getAllUsersAsDTO() {
        try {
            List<Registereduser> users = userRepo.findAll();
            return users.stream()
                .map(UserListDTO::fromEntity)
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error getting all users as DTOs: " + e.getMessage());
            throw e;
        }
    }
}
