-- Schema3.sql - Subscriber table creation
-- This file follows the incremental schema pattern used in the project

-- Create subscriber table
CREATE TABLE IF NOT EXISTS subscriber (
    subscriber_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    is_subscriber B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_subscriber_email ON subscriber(email);

-- <PERSON>reate index on is_subscriber for filtering
CREATE INDEX IF NOT EXISTS idx_subscriber_is_subscriber ON subscriber(is_subscriber);
