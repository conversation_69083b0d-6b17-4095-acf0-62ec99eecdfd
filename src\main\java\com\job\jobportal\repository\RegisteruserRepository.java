package com.job.jobportal.repository;

import com.job.jobportal.dto.MarketingUserDTO;
import com.job.jobportal.model.AccountDetails;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.security.AuthProvider;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
@Transactional
public interface RegisteruserRepository extends JpaRepository<Registereduser, Long> {

    Optional<Registereduser> findByEmail(String emailId);

    Registereduser findByUsername(String username);


    Boolean existsByEmail(String email);

    @Query("select r from Registereduser  r inner join r.roles u  where u.rolename=:rolename")
    List<Registereduser> findAllByMarketingUser(@Param("rolename") String rolename);

    @Query("select r from Registereduser r inner join r.roles u where u.rolename=:rolename order by r.createdOn desc")
    Page<Registereduser> findAllByMarketingUserPaginated(@Param("rolename") String rolename, Pageable pageable);

    @Query("""
        SELECT new com.job.jobportal.dto.MarketingUserDTO(
            CONCAT(r.firstname, COALESCE(CONCAT(' ', r.lastname), '')),
            r.mobileno,
            r.email,
            r.createdOn,
            r.accountDetails.isActive
        )
        FROM Registereduser r
        INNER JOIN r.roles role
        FULL OUTER JOIN Subscriber s ON r.email = s.email
        WHERE role.rolename = :rolename
        AND (:userType IS NULL
             OR (:userType = 'USER' AND r.userid IS NOT NULL)
             OR (:userType = 'SUBSCRIBER' AND s.subscriberId IS NOT NULL))
        ORDER BY COALESCE(r.createdOn, s.createdAt) DESC
        """)
    Page<MarketingUserDTO> findAllUserTypes(@Param("rolename") String rolename, @Param("userType") String userType, Pageable pageable);

    @Modifying
    @Query("UPDATE Registereduser u SET u.refreshToken =:refreshToken WHERE u.userid =:userid")
    void updateRefreshToken(@Param("refreshToken") String refreshToken, @Param("userid") Long userid);

    @Modifying
    @Query("UPDATE Registereduser u SET u.notificationToken =:notificationToken WHERE u.userid =:userid")
    void updateNotificationToken(@Param("notificationToken") String notificationToken, @Param("userid") Long userid);

    @Query("select r from Registereduser  r  where r.notificationToken IS NOT NULL AND r.notificationToken <> ''")
    List<Registereduser> findAllByNotificationToken();

    @Modifying
    @Query("UPDATE Registereduser u SET u.provider =:provider WHERE u.username =:username")
    void updateAuthenticationType(@Param("provider") AuthProvider authType, @Param("username") String username);


    @Modifying
    @Query("UPDATE Registereduser u SET u.password =:password  WHERE u.userid =:userid")
    int updatePassword(@Param("password") String password, @Param("userid") Long userid);


    @Modifying
    @Query("UPDATE Registereduser u SET u.password =:password ,u.hasPassword=:hasPassword WHERE u.userid =:userid")
    int updatePasswordwithAuthType(@Param("password") String password, @Param("hasPassword") int hasPassword, @Param("userid") Long userid);


    @Modifying
    @Query("UPDATE Registereduser u SET u.firstname =:firstname , u.lastname=:lastname ,u.mobileno =:mobileno WHERE u.userid =:userid")
    int updateUserDetails(@Param("firstname") String firstname, @Param("lastname") String lastname, @Param("mobileno") String mobileno, @Param("userid") Long userid);

    Optional<Registereduser> findByAccountDetails(AccountDetails accountDetails);

    @Query("SELECT u FROM Registereduser u LEFT JOIN FETCH u.companyProfile WHERE u.userid = :userid")
    Optional<Registereduser> findCompanyProfileByUserId(@Param("userid")Long userId);

    boolean existsByMobileno(String mobileNumber);

    Registereduser findByMobileno(String mobileNumber);

    @Query("SELECT DISTINCT r FROM Registereduser r " +
           "LEFT JOIN FETCH r.roles role " +
           "LEFT JOIN FETCH r.accountDetails " +
           "WHERE (:role IS NULL OR role.rolename = :role) " +
           "AND (:email IS NULL OR LOWER(r.email) LIKE LOWER(CONCAT('%', :email, '%')))")
    List<Registereduser> findByRoleAndEmailContainingFetchAll(
            @Param("role") String role,
            @Param("email") String email);
}
